/**
 * @fileoverview Package exports for @eslint/eslintrc
 * <AUTHOR>
 */
//------------------------------------------------------------------------------
// Requirements
//------------------------------------------------------------------------------

import * as ConfigOps from "./shared/config-ops.js";
import ConfigValidator from "./shared/config-validator.js";
import * as naming from "./shared/naming.js";
import environments from "../conf/environments.js";

//-----------------------------------------------------------------------------
// Exports
//-----------------------------------------------------------------------------

const Legacy = {
    environments,

    // shared
    ConfigOps,
    ConfigValidator,
    naming
};

export {
    Legacy
};
