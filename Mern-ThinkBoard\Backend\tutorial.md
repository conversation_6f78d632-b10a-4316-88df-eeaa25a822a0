### Tutorial of backend with Nodejs, Expressjs, MongoDB

• if we type on terminal "npm init -y", it will create a package.json file.

• if we want to use ES modules, we need to add "type": "module", in package.json file.
• if we didn't add "type": "module", in package.json file, we need to use "require" to import modules.
• if we add "type": "module", in package.json file, we need to use "import" to import modules.

```js
import express from 'express';

// This will work if in the package in json file we have "type": "module",
// otherwise we need to use "require" to import express.
const express = require('express');
// This will not work if in the package in json file we have "type": "commonjs",
```

• in package.json if we want to run this statement in terminal `npm run dev` we have to add in the scripts section. `"dev": "node server.js"` to run the server.js file. with `npm run dev` statement in terminal otherwise it will work in terminal with the `node server.js` statement and will not work with `npm run dev` statement.

## When we want to update a something in the code in `server.js`

1. we need to stop the server with `ctrl + c` in terminal.
2. we need to run the server again with `npm run dev` in terminal.

• this is the way to run the server in development mode. and it is anoying to stop and run the server again and again when we make changes in the code. so we use `nodemon` to run the server in development mode. and it will automatically restart the server when we make changes in the code.

• so to install `nodemon` we need to run `npm install -D nodemon` in terminal. and `-D` is for devDependencies. and it will be added in the `package.json` file in the `devDependencies` section.

• and to run the server with `nodemon` we need to add in the scripts section. `"start": "nodemon server.js"` to run the server.js file. with `npm start` statement in terminal. and it will automatically restart the server when we make changes in the code.

## Hint - Very important

- keep in mind that in `package.json` in script section we have two scripts

  1. `dev` - to run the server in development mode with `nodemon`.
  2. `start` - to run the server in production mode with `node`.

  • and we will use `npm start` to run the server in production mode.

```json
  "scripts": {
    // we will use this script to run the server in development mode with nodemon
    "dev": "nodemon server.js", // for development mode
    "start": "node server.js" // for production mode
  },
```

# make the routes or endpoints of API in `server.js` file more manageable. so we will use the router in routes folder and use it here with a prefix /api/notes which is the base url for all the notes routes.

`server.js`

```js
import express from 'express';
import notesRoutes from './routes/notesRoutes.js';

const app = express();

// to make the routes more manageable we will use the router in routes folder and use it here with a prefix /api/notes which is the base url for all the notes routes
app.use('/api/notes', notesRoutes);

// make a get request to this route api/notes and sent a response
// app.get('/api/notes', (req, res) => {
//   res.status(200).send('<h1>You Got 15 Notes</h1>');
// });

// app.post('/api/notes', (req, res) => {
//   res.status(201).json({ message: 'Note created Successfully!' });
// });

// app.put('/api/notes/:id', (req, res) => {
//   res.status(200).json({ message: 'Note updated Successfully!' });
// });

// app.delete('/api/notes/:id', (req, res) => {
//   res.status(200).json({ message: 'Note deleted Successfully!' });
// });

app.listen(5001, () => {
  console.log('Server is running on port 5001');
});
```

`routes/notesRoutes.js`

```js
import express from 'express';

const router = express.Router();

router.get('/', (req, res) => {
  res.status(200).send('<h1>You Got 300 Notes</h1>');
});

export default router;
```
