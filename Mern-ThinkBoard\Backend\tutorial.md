### Tutorial of backend with Nodejs, Expressjs, MongoDB

• if we type on terminal "npm init -y", it will create a package.json file.

• if we want to use ES modules, we need to add "type": "module", in package.json file.
• if we didn't add "type": "module", in package.json file, we need to use "require" to import modules.
• if we add "type": "module", in package.json file, we need to use "import" to import modules.

```js
import express from 'express';

// This will work if in the package in json file we have "type": "module",
// otherwise we need to use "require" to import express.
const express = require('express');
// This will not work if in the package in json file we have "type": "commonjs",
```

• in package.json if we want to run this statement in terminal `npm run dev` we have to add in the scripts section. `"dev": "node server.js"` to run the server.js file. with `npm run dev` statement in terminal otherwise it will work in terminal with the `node server.js` statement and will not work with `npm run dev` statement.

## When we want to update a something in the code in `server.js`

1. we need to stop the server with `ctrl + c` in terminal.
2. we need to run the server again with `npm run dev` in terminal.

• this is the way to run the server in development mode. and it is anoying to stop and run the server again and again when we make changes in the code. so we use `nodemon` to run the server in development mode. and it will automatically restart the server when we make changes in the code.

• so to install `nodemon` we need to run `npm install -D nodemon` in terminal. and `-D` is for devDependencies. and it will be added in the `package.json` file in the `devDependencies` section.

• and to run the server with `nodemon` we need to add in the scripts section. `"start": "nodemon server.js"` to run the server.js file. with `npm start` statement in terminal. and it will automatically restart the server when we make changes in the code.
