import Note from '../models/Note.js';

export const getAllNotes = async (req, res) => {
  try {
    const note = await Note.find(); // find all notes from database
    res.status(200).json(note);
  } catch (error) {
    console.error('GetAllNotes Controller Not Working As Expected!', error);
    res
      .status(500)
      .json({ message: 'Something Went Wrong While Fetching Notes!' });
  }
};

export const createNote = async (req, res) => {
  try {
    const { title, content } = req.body;
    // we will get undefined undefined because we need to use middleware to parse the body of the request
    // we will use express.json() middleware to parse the body of the request
    // console.log(title, content);
    const newNote = new Note({ title: title, content: content }); // create a new note object with title and content properties
    await newNote.save(); // save the new note object in database
    res.status(201).json({ message: 'Note Created Successfully!' });
  } catch (error) {
    console.error('CreateNote Controller Not Working As Expected!', error);
    res
      .status(500)
      .json({ message: 'Something Went Wrong While Creating Note!' });
  }
};

export const updateNote = (req, res) => {
  res.status(200).json({ message: 'Note updated Successfully!' });
};

export const deleteNote = (req, res) => {
  res.status(200).json({ message: 'Note deleted Successfully!' });
};
