import express from 'express';
import notesRoutes from './routes/notesRoutes.js';
import { connectDb } from './config/db.js';
import dotenv from 'dotenv';

// load environment variables from .env file
dotenv.config();

// console.log(process.env.MONGODB_URI); // HERE I WILL GET undefined if I don't use dotenv package and use dotenv.config() in server.js file.

const app = express();

// Connect to MongoDB
connectDb();

// middleware to parse the body of the request
app.use(express.json());

// to make the routes more manageable we will use the router in routes folder and use it here with a prefix /api/notes which is the base url for all the notes routes
app.use('/api/notes', notesRoutes);

app.listen(process.env.PORT, () => {
  console.log(`Server is running on port ${process.env.PORT}`);
});
