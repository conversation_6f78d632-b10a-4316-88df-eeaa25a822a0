{"version": 3, "names": ["_extends", "exports", "default", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply"], "sources": ["../../src/helpers/extends.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\ntype Intersection<R extends any[]> = R extends [infer H, ...infer S]\n  ? H & Intersection<S>\n  : unknown;\n\nexport default function _extends<T extends object, U extends unknown[]>(\n  target: T,\n  ...sources: U\n): T & Intersection<U>;\nexport default function _extends() {\n  // @ts-expect-error explicitly assign to function\n  _extends = Object.assign\n    ? // need a bind because https://github.com/babel/babel/issues/14527\n      // @ts-expect-error -- intentionally omitting the argument\n      Object.assign.bind(/* undefined */)\n    : function (target: any) {\n        for (var i = 1; i < arguments.length; i++) {\n          var source = arguments[i];\n          for (var key in source) {\n            if (Object.prototype.hasOwnProperty.call(source, key)) {\n              target[key] = source[key];\n            }\n          }\n        }\n        return target;\n      };\n\n  return _extends.apply(\n    null,\n    arguments as any as [source: object, ...target: any[]],\n  );\n}\n"], "mappings": ";;;;;;AAUe,SAASA,QAAQA,CAAA,EAAG;EAEjCC,OAAA,CAAAC,OAAA,GAAAF,QAAQ,GAAGG,MAAM,CAACC,MAAM,GAGpBD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAgB,CAAC,GACnC,UAAUC,MAAW,EAAE;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MACzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QACtB,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACrDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC3B;MACF;IACF;IACA,OAAOL,MAAM;EACf,CAAC;EAEL,OAAON,QAAQ,CAACe,KAAK,CACnB,IAAI,EACJP,SACF,CAAC;AACH", "ignoreList": []}