{"version": 3, "file": "encrypter.js", "sourceRoot": "", "sources": ["../src/encrypter.ts"], "names": [], "mappings": ";;;AAAA,4EAAoG;AACpG,2CAAkD;AAClD,iCAAoD;AACpD,mCAAiF;AACjF,iDAAsE;AAQtE,gBAAgB;AAChB,MAAa,SAAS;IAMpB,YAAY,MAAmB,EAAE,GAAW,EAAE,OAA2B;QACvE,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YAC/C,MAAM,IAAI,iCAAyB,CAAC,2CAA2C,CAAC,CAAC;QACnF,CAAC;QACD,2HAA2H;QAC3H,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB,CAAC;QAC1E,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YAC/E,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC;QACjD,CAAC;aAAM,IAAI,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;YACzD,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,SAAS,CAAC;QACpD,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,CAAC,cAAc,CAAC,YAAY,GAAG;gBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,aAAa,EAAE,OAAO,CAAC,aAAa;aACrC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAa,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAED,iBAAiB,CAAC,MAAmB,EAAE,GAAW,EAAE,OAA2B;QAC7E,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzC,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAuB,EAAE,CAAC;YAE7C,KAAK,MAAM,GAAG,IAAI;gBAChB,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACtC,GAAG,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC;aAC7B,EAAE,CAAC;gBACd,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACvF,SAAS;gBACX,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7D,CAAC;YAED,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;YAE9B,cAAc,GAAG,IAAI,0BAAW,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;YAErC,KAAK,MAAM,SAAS,IAAI,+BAAmB,EAAE,CAAC;gBAC5C,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnD,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gBAC/C,cAAc,EAAE,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,IAAI,CAAC,eAAe,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YACnD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,MAAM,cAAc,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAmB;QAC7B,IAAI,KAAK,CAAC;QACV,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,kBAAkB,EAAE,CAAC;YAC5B,KAAK,GAAG,kBAAkB,CAAC;QAC7B,CAAC;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,cAAc,IAAI,IAAI,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;YACxD,OAAO,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM,CAAC,kBAAkB;QACvB,MAAM,uBAAuB,GAAG,IAAA,iCAA0B,GAAE,CAAC;QAC7D,IAAI,cAAc,IAAI,uBAAuB,EAAE,CAAC;YAC9C,MAAM,IAAI,mCAA2B,CACnC,8DAA8D;gBAC5D,wEAAwE,EAC1E;gBACE,KAAK,EAAE,uBAAuB,CAAC,cAAc,CAAC;gBAC9C,cAAc,EAAE,2BAA2B;aAC5C,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjHD,8BAiHC"}