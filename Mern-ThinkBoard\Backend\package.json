{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mern-thinkboard": "file:..", "mongoose": "^8.18.0"}, "devDependencies": {"nodemon": "^3.1.10"}}