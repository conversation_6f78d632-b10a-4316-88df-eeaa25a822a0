(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function r(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(s){if(s.ep)return;s.ep=!0;const f=r(s);fetch(s.href,f)}})();function Yv(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var ds={exports:{}},Qi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _m;function Gv(){if(_m)return Qi;_m=1;var n=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function r(o,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var y in s)y!=="key"&&(f[y]=s[y])}else f=s;return s=f.ref,{$$typeof:n,type:o,key:d,ref:s!==void 0?s:null,props:f}}return Qi.Fragment=i,Qi.jsx=r,Qi.jsxs=r,Qi}var zm;function Xv(){return zm||(zm=1,ds.exports=Gv()),ds.exports}var q=Xv(),hs={exports:{}},xe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm;function Vv(){if(Cm)return xe;Cm=1;var n=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),b=Symbol.iterator;function T(E){return E===null||typeof E!="object"?null:(E=b&&E[b]||E["@@iterator"],typeof E=="function"?E:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,Q={};function H(E,X,I){this.props=E,this.context=X,this.refs=Q,this.updater=I||M}H.prototype.isReactComponent={},H.prototype.setState=function(E,X){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,X,"setState")},H.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function K(){}K.prototype=H.prototype;function J(E,X,I){this.props=E,this.context=X,this.refs=Q,this.updater=I||M}var F=J.prototype=new K;F.constructor=J,U(F,H.prototype),F.isPureReactComponent=!0;var ye=Array.isArray,$={H:null,A:null,T:null,S:null,V:null},_=Object.prototype.hasOwnProperty;function ce(E,X,I,P,ue,we){return I=we.ref,{$$typeof:n,type:E,key:X,ref:I!==void 0?I:null,props:we}}function ge(E,X){return ce(E.type,X,void 0,void 0,void 0,E.props)}function pe(E){return typeof E=="object"&&E!==null&&E.$$typeof===n}function Ue(E){var X={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(I){return X[I]})}var Pe=/\/+/g;function Fe(E,X){return typeof E=="object"&&E!==null&&E.key!=null?Ue(""+E.key):X.toString(36)}function ze(){}function Le(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(ze,ze):(E.status="pending",E.then(function(X){E.status==="pending"&&(E.status="fulfilled",E.value=X)},function(X){E.status==="pending"&&(E.status="rejected",E.reason=X)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function Be(E,X,I,P,ue){var we=typeof E;(we==="undefined"||we==="boolean")&&(E=null);var he=!1;if(E===null)he=!0;else switch(we){case"bigint":case"string":case"number":he=!0;break;case"object":switch(E.$$typeof){case n:case i:he=!0;break;case g:return he=E._init,Be(he(E._payload),X,I,P,ue)}}if(he)return ue=ue(E),he=P===""?"."+Fe(E,0):P,ye(ue)?(I="",he!=null&&(I=he.replace(Pe,"$&/")+"/"),Be(ue,X,I,"",function(ha){return ha})):ue!=null&&(pe(ue)&&(ue=ge(ue,I+(ue.key==null||E&&E.key===ue.key?"":(""+ue.key).replace(Pe,"$&/")+"/")+he)),X.push(ue)),1;he=0;var Ot=P===""?".":P+":";if(ye(E))for(var ke=0;ke<E.length;ke++)P=E[ke],we=Ot+Fe(P,ke),he+=Be(P,X,I,we,ue);else if(ke=T(E),typeof ke=="function")for(E=ke.call(E),ke=0;!(P=E.next()).done;)P=P.value,we=Ot+Fe(P,ke++),he+=Be(P,X,I,we,ue);else if(we==="object"){if(typeof E.then=="function")return Be(Le(E),X,I,P,ue);throw X=String(E),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return he}function C(E,X,I){if(E==null)return E;var P=[],ue=0;return Be(E,P,"","",function(we){return X.call(I,we,ue++)}),P}function W(E){if(E._status===-1){var X=E._result;X=X(),X.then(function(I){(E._status===0||E._status===-1)&&(E._status=1,E._result=I)},function(I){(E._status===0||E._status===-1)&&(E._status=2,E._result=I)}),E._status===-1&&(E._status=0,E._result=X)}if(E._status===1)return E._result.default;throw E._result}var ee=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function Te(){}return xe.Children={map:C,forEach:function(E,X,I){C(E,function(){X.apply(this,arguments)},I)},count:function(E){var X=0;return C(E,function(){X++}),X},toArray:function(E){return C(E,function(X){return X})||[]},only:function(E){if(!pe(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},xe.Component=H,xe.Fragment=r,xe.Profiler=s,xe.PureComponent=J,xe.StrictMode=o,xe.Suspense=p,xe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=$,xe.__COMPILER_RUNTIME={__proto__:null,c:function(E){return $.H.useMemoCache(E)}},xe.cache=function(E){return function(){return E.apply(null,arguments)}},xe.cloneElement=function(E,X,I){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var P=U({},E.props),ue=E.key,we=void 0;if(X!=null)for(he in X.ref!==void 0&&(we=void 0),X.key!==void 0&&(ue=""+X.key),X)!_.call(X,he)||he==="key"||he==="__self"||he==="__source"||he==="ref"&&X.ref===void 0||(P[he]=X[he]);var he=arguments.length-2;if(he===1)P.children=I;else if(1<he){for(var Ot=Array(he),ke=0;ke<he;ke++)Ot[ke]=arguments[ke+2];P.children=Ot}return ce(E.type,ue,void 0,void 0,we,P)},xe.createContext=function(E){return E={$$typeof:d,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:f,_context:E},E},xe.createElement=function(E,X,I){var P,ue={},we=null;if(X!=null)for(P in X.key!==void 0&&(we=""+X.key),X)_.call(X,P)&&P!=="key"&&P!=="__self"&&P!=="__source"&&(ue[P]=X[P]);var he=arguments.length-2;if(he===1)ue.children=I;else if(1<he){for(var Ot=Array(he),ke=0;ke<he;ke++)Ot[ke]=arguments[ke+2];ue.children=Ot}if(E&&E.defaultProps)for(P in he=E.defaultProps,he)ue[P]===void 0&&(ue[P]=he[P]);return ce(E,we,void 0,void 0,null,ue)},xe.createRef=function(){return{current:null}},xe.forwardRef=function(E){return{$$typeof:y,render:E}},xe.isValidElement=pe,xe.lazy=function(E){return{$$typeof:g,_payload:{_status:-1,_result:E},_init:W}},xe.memo=function(E,X){return{$$typeof:m,type:E,compare:X===void 0?null:X}},xe.startTransition=function(E){var X=$.T,I={};$.T=I;try{var P=E(),ue=$.S;ue!==null&&ue(I,P),typeof P=="object"&&P!==null&&typeof P.then=="function"&&P.then(Te,ee)}catch(we){ee(we)}finally{$.T=X}},xe.unstable_useCacheRefresh=function(){return $.H.useCacheRefresh()},xe.use=function(E){return $.H.use(E)},xe.useActionState=function(E,X,I){return $.H.useActionState(E,X,I)},xe.useCallback=function(E,X){return $.H.useCallback(E,X)},xe.useContext=function(E){return $.H.useContext(E)},xe.useDebugValue=function(){},xe.useDeferredValue=function(E,X){return $.H.useDeferredValue(E,X)},xe.useEffect=function(E,X,I){var P=$.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return P.useEffect(E,X)},xe.useId=function(){return $.H.useId()},xe.useImperativeHandle=function(E,X,I){return $.H.useImperativeHandle(E,X,I)},xe.useInsertionEffect=function(E,X){return $.H.useInsertionEffect(E,X)},xe.useLayoutEffect=function(E,X){return $.H.useLayoutEffect(E,X)},xe.useMemo=function(E,X){return $.H.useMemo(E,X)},xe.useOptimistic=function(E,X){return $.H.useOptimistic(E,X)},xe.useReducer=function(E,X,I){return $.H.useReducer(E,X,I)},xe.useRef=function(E){return $.H.useRef(E)},xe.useState=function(E){return $.H.useState(E)},xe.useSyncExternalStore=function(E,X,I){return $.H.useSyncExternalStore(E,X,I)},xe.useTransition=function(){return $.H.useTransition()},xe.version="19.1.1",xe}var Um;function Bs(){return Um||(Um=1,hs.exports=Vv()),hs.exports}var O=Bs();const Wl=Yv(O);var ms={exports:{}},Zi={},ys={exports:{}},ps={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lm;function Qv(){return Lm||(Lm=1,(function(n){function i(C,W){var ee=C.length;C.push(W);e:for(;0<ee;){var Te=ee-1>>>1,E=C[Te];if(0<s(E,W))C[Te]=W,C[ee]=E,ee=Te;else break e}}function r(C){return C.length===0?null:C[0]}function o(C){if(C.length===0)return null;var W=C[0],ee=C.pop();if(ee!==W){C[0]=ee;e:for(var Te=0,E=C.length,X=E>>>1;Te<X;){var I=2*(Te+1)-1,P=C[I],ue=I+1,we=C[ue];if(0>s(P,ee))ue<E&&0>s(we,P)?(C[Te]=we,C[ue]=ee,Te=ue):(C[Te]=P,C[I]=ee,Te=I);else if(ue<E&&0>s(we,ee))C[Te]=we,C[ue]=ee,Te=ue;else break e}}return W}function s(C,W){var ee=C.sortIndex-W.sortIndex;return ee!==0?ee:C.id-W.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var d=Date,y=d.now();n.unstable_now=function(){return d.now()-y}}var p=[],m=[],g=1,b=null,T=3,M=!1,U=!1,Q=!1,H=!1,K=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function ye(C){for(var W=r(m);W!==null;){if(W.callback===null)o(m);else if(W.startTime<=C)o(m),W.sortIndex=W.expirationTime,i(p,W);else break;W=r(m)}}function $(C){if(Q=!1,ye(C),!U)if(r(p)!==null)U=!0,_||(_=!0,Fe());else{var W=r(m);W!==null&&Be($,W.startTime-C)}}var _=!1,ce=-1,ge=5,pe=-1;function Ue(){return H?!0:!(n.unstable_now()-pe<ge)}function Pe(){if(H=!1,_){var C=n.unstable_now();pe=C;var W=!0;try{e:{U=!1,Q&&(Q=!1,J(ce),ce=-1),M=!0;var ee=T;try{t:{for(ye(C),b=r(p);b!==null&&!(b.expirationTime>C&&Ue());){var Te=b.callback;if(typeof Te=="function"){b.callback=null,T=b.priorityLevel;var E=Te(b.expirationTime<=C);if(C=n.unstable_now(),typeof E=="function"){b.callback=E,ye(C),W=!0;break t}b===r(p)&&o(p),ye(C)}else o(p);b=r(p)}if(b!==null)W=!0;else{var X=r(m);X!==null&&Be($,X.startTime-C),W=!1}}break e}finally{b=null,T=ee,M=!1}W=void 0}}finally{W?Fe():_=!1}}}var Fe;if(typeof F=="function")Fe=function(){F(Pe)};else if(typeof MessageChannel<"u"){var ze=new MessageChannel,Le=ze.port2;ze.port1.onmessage=Pe,Fe=function(){Le.postMessage(null)}}else Fe=function(){K(Pe,0)};function Be(C,W){ce=K(function(){C(n.unstable_now())},W)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(C){C.callback=null},n.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ge=0<C?Math.floor(1e3/C):5},n.unstable_getCurrentPriorityLevel=function(){return T},n.unstable_next=function(C){switch(T){case 1:case 2:case 3:var W=3;break;default:W=T}var ee=T;T=W;try{return C()}finally{T=ee}},n.unstable_requestPaint=function(){H=!0},n.unstable_runWithPriority=function(C,W){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var ee=T;T=C;try{return W()}finally{T=ee}},n.unstable_scheduleCallback=function(C,W,ee){var Te=n.unstable_now();switch(typeof ee=="object"&&ee!==null?(ee=ee.delay,ee=typeof ee=="number"&&0<ee?Te+ee:Te):ee=Te,C){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=ee+E,C={id:g++,callback:W,priorityLevel:C,startTime:ee,expirationTime:E,sortIndex:-1},ee>Te?(C.sortIndex=ee,i(m,C),r(p)===null&&C===r(m)&&(Q?(J(ce),ce=-1):Q=!0,Be($,ee-Te))):(C.sortIndex=E,i(p,C),U||M||(U=!0,_||(_=!0,Fe()))),C},n.unstable_shouldYield=Ue,n.unstable_wrapCallback=function(C){var W=T;return function(){var ee=T;T=W;try{return C.apply(this,arguments)}finally{T=ee}}}})(ps)),ps}var jm;function Zv(){return jm||(jm=1,ys.exports=Qv()),ys.exports}var vs={exports:{}},Rt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm;function Kv(){if(Hm)return Rt;Hm=1;var n=Bs();function i(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)m+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(i(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(p,m,g){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:b==null?null:""+b,children:p,containerInfo:m,implementation:g}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Rt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Rt.createPortal=function(p,m){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return f(p,m,null,g)},Rt.flushSync=function(p){var m=d.T,g=o.p;try{if(d.T=null,o.p=2,p)return p()}finally{d.T=m,o.p=g,o.d.f()}},Rt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,o.d.C(p,m))},Rt.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},Rt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var g=m.as,b=y(g,m.crossOrigin),T=typeof m.integrity=="string"?m.integrity:void 0,M=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;g==="style"?o.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:b,integrity:T,fetchPriority:M}):g==="script"&&o.d.X(p,{crossOrigin:b,integrity:T,fetchPriority:M,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Rt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var g=y(m.as,m.crossOrigin);o.d.M(p,{crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&o.d.M(p)},Rt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var g=m.as,b=y(g,m.crossOrigin);o.d.L(p,g,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Rt.preloadModule=function(p,m){if(typeof p=="string")if(m){var g=y(m.as,m.crossOrigin);o.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:g,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else o.d.m(p)},Rt.requestFormReset=function(p){o.d.r(p)},Rt.unstable_batchedUpdates=function(p,m){return p(m)},Rt.useFormState=function(p,m,g){return d.H.useFormState(p,m,g)},Rt.useFormStatus=function(){return d.H.useHostTransitionStatus()},Rt.version="19.1.1",Rt}var Bm;function kv(){if(Bm)return vs.exports;Bm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}return n(),vs.exports=Kv(),vs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function Jv(){if(qm)return Zi;qm=1;var n=Zv(),i=Bs(),r=kv();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function y(e){if(f(e)!==e)throw Error(o(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var a=e,l=t;;){var u=a.return;if(u===null)break;var c=u.alternate;if(c===null){if(l=u.return,l!==null){a=l;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===a)return y(u),e;if(c===l)return y(u),t;c=c.sibling}throw Error(o(188))}if(a.return!==l.return)a=u,l=c;else{for(var h=!1,v=u.child;v;){if(v===a){h=!0,a=u,l=c;break}if(v===l){h=!0,l=u,a=c;break}v=v.sibling}if(!h){for(v=c.child;v;){if(v===a){h=!0,a=c,l=u;break}if(v===l){h=!0,l=c,a=u;break}v=v.sibling}if(!h)throw Error(o(189))}}if(a.alternate!==l)throw Error(o(190))}if(a.tag!==3)throw Error(o(188));return a.stateNode.current===a?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var g=Object.assign,b=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),Q=Symbol.for("react.strict_mode"),H=Symbol.for("react.profiler"),K=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),F=Symbol.for("react.context"),ye=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),ce=Symbol.for("react.memo"),ge=Symbol.for("react.lazy"),pe=Symbol.for("react.activity"),Ue=Symbol.for("react.memo_cache_sentinel"),Pe=Symbol.iterator;function Fe(e){return e===null||typeof e!="object"?null:(e=Pe&&e[Pe]||e["@@iterator"],typeof e=="function"?e:null)}var ze=Symbol.for("react.client.reference");function Le(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ze?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case H:return"Profiler";case Q:return"StrictMode";case $:return"Suspense";case _:return"SuspenseList";case pe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case M:return"Portal";case F:return(e.displayName||"Context")+".Provider";case J:return(e._context.displayName||"Context")+".Consumer";case ye:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ce:return t=e.displayName||null,t!==null?t:Le(e.type)||"Memo";case ge:t=e._payload,e=e._init;try{return Le(e(t))}catch{}}return null}var Be=Array.isArray,C=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ee={pending:!1,data:null,method:null,action:null},Te=[],E=-1;function X(e){return{current:e}}function I(e){0>E||(e.current=Te[E],Te[E]=null,E--)}function P(e,t){E++,Te[E]=e.current,e.current=t}var ue=X(null),we=X(null),he=X(null),Ot=X(null);function ke(e,t){switch(P(he,t),P(we,e),P(ue,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?im(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=im(t),e=um(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(ue),P(ue,e)}function ha(){I(ue),I(we),I(he)}function ot(e){e.memoizedState!==null&&P(Ot,e);var t=ue.current,a=um(t,e.type);t!==a&&(P(we,e),P(ue,a))}function ia(e){we.current===e&&(I(ue),I(we)),Ot.current===e&&(I(Ot),qi._currentValue=ee)}var ln=Object.prototype.hasOwnProperty,Jn=n.unstable_scheduleCallback,ua=n.unstable_cancelCallback,no=n.unstable_shouldYield,io=n.unstable_requestPaint,Ht=n.unstable_now,uo=n.unstable_getCurrentPriorityLevel,fu=n.unstable_ImmediatePriority,du=n.unstable_UserBlockingPriority,nn=n.unstable_NormalPriority,wa=n.unstable_LowPriority,Fa=n.unstable_IdlePriority,hu=n.log,Fn=n.unstable_setDisableYieldValue,Dt=null,Ie=null;function ra(e){if(typeof hu=="function"&&Fn(e),Ie&&typeof Ie.setStrictMode=="function")try{Ie.setStrictMode(Dt,e)}catch{}}var gt=Math.clz32?Math.clz32:mu,ro=Math.log,ma=Math.LN2;function mu(e){return e>>>=0,e===0?32:31-(ro(e)/ma|0)|0}var wl=256,Dl=4194304;function Da(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ml(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var u=0,c=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var v=l&134217727;return v!==0?(l=v&~c,l!==0?u=Da(l):(h&=v,h!==0?u=Da(h):a||(a=v&~e,a!==0&&(u=Da(a))))):(v=l&~c,v!==0?u=Da(v):h!==0?u=Da(h):a||(a=l&~e,a!==0&&(u=Da(a)))),u===0?0:t!==0&&t!==u&&(t&c)===0&&(c=u&-u,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:u}function ya(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function yu(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function un(){var e=wl;return wl<<=1,(wl&4194048)===0&&(wl=256),e}function pu(){var e=Dl;return Dl<<=1,(Dl&62914560)===0&&(Dl=4194304),e}function rn(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Nl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function vu(e,t,a,l,u,c){var h=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var v=e.entanglements,S=e.expirationTimes,N=e.hiddenUpdates;for(a=h&~a;0<a;){var Y=31-gt(a),V=1<<Y;v[Y]=0,S[Y]=-1;var z=N[Y];if(z!==null)for(N[Y]=null,Y=0;Y<z.length;Y++){var L=z[Y];L!==null&&(L.lane&=-536870913)}a&=~V}l!==0&&_l(e,l,0),c!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=c&~(h&~t))}function _l(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-gt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function zl(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-gt(a),u=1<<l;u&t|e[l]&t&&(e[l]|=t),a&=~u}}function $n(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Wn(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function x(){var e=W.p;return e!==0?e:(e=window.event,e===void 0?32:Om(e.type))}function w(e,t){var a=W.p;try{return W.p=e,t()}finally{W.p=a}}var j=Math.random().toString(36).slice(2),Z="__reactFiber$"+j,k="__reactProps$"+j,ae="__reactContainer$"+j,re="__reactEvents$"+j,le="__reactListeners$"+j,se="__reactHandles$"+j,fe="__reactResources$"+j,ne="__reactMarker$"+j;function ie(e){delete e[Z],delete e[k],delete e[re],delete e[le],delete e[se]}function Re(e){var t=e[Z];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ae]||a[Z]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=sm(e);e!==null;){if(a=e[Z])return a;e=sm(e)}return t}e=a,a=e.parentNode}return null}function je(e){if(e=e[Z]||e[ae]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function $e(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function at(e){var t=e[fe];return t||(t=e[fe]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function be(e){e[ne]=!0}var Ge=new Set,pa={};function Bt(e,t){At(e,t),At(e+"Capture",t)}function At(e,t){for(pa[e]=t,e=0;e<t.length;e++)Ge.add(t[e])}var kt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),on={},$a={};function Ma(e){return ln.call($a,e)?!0:ln.call(on,e)?!1:kt.test(e)?$a[e]=!0:(on[e]=!0,!1)}function Na(e,t,a){if(Ma(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function _a(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Se(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var ct,za;function Mt(e){if(ct===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);ct=t&&t[1]||"",za=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ct+e+za}var et=!1;function Wa(e,t){if(!e||et)return"";et=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(L){var z=L}Reflect.construct(e,[],V)}else{try{V.call()}catch(L){z=L}e.call(V.prototype)}}else{try{throw Error()}catch(L){z=L}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(L){if(L&&z&&typeof L.stack=="string")return[L.stack,z.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),h=c[0],v=c[1];if(h&&v){var S=h.split(`
`),N=v.split(`
`);for(u=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;u<N.length&&!N[u].includes("DetermineComponentFrameRoot");)u++;if(l===S.length||u===N.length)for(l=S.length-1,u=N.length-1;1<=l&&0<=u&&S[l]!==N[u];)u--;for(;1<=l&&0<=u;l--,u--)if(S[l]!==N[u]){if(l!==1||u!==1)do if(l--,u--,0>u||S[l]!==N[u]){var Y=`
`+S[l].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=l&&0<=u);break}}}finally{et=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Mt(a):""}function gu(e){switch(e.tag){case 26:case 27:case 5:return Mt(e.type);case 16:return Mt("Lazy");case 13:return Mt("Suspense");case 19:return Mt("SuspenseList");case 0:case 15:return Wa(e.type,!1);case 11:return Wa(e.type.render,!1);case 1:return Wa(e.type,!0);case 31:return Mt("Activity");default:return""}}function bu(e){try{var t="";do t+=gu(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Jt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Is(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function L0(e){var t=Is(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var u=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(h){l=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Su(e){e._valueTracker||(e._valueTracker=L0(e))}function ef(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Is(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Eu(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var j0=/[\n"\\]/g;function Ft(e){return e.replace(j0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function oo(e,t,a,l,u,c,h,v){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Jt(t)):e.value!==""+Jt(t)&&(e.value=""+Jt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?co(e,h,Jt(t)):a!=null?co(e,h,Jt(a)):l!=null&&e.removeAttribute("value"),u==null&&c!=null&&(e.defaultChecked=!!c),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?e.name=""+Jt(v):e.removeAttribute("name")}function tf(e,t,a,l,u,c,h,v){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+Jt(a):"",t=t!=null?""+Jt(t):a,v||t===e.value||(e.value=t),e.defaultValue=t}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=v?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function co(e,t,a){t==="number"&&Eu(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function cn(e,t,a,l){if(e=e.options,t){t={};for(var u=0;u<a.length;u++)t["$"+a[u]]=!0;for(a=0;a<e.length;a++)u=t.hasOwnProperty("$"+e[a].value),e[a].selected!==u&&(e[a].selected=u),u&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Jt(a),t=null,u=0;u<e.length;u++){if(e[u].value===a){e[u].selected=!0,l&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function af(e,t,a){if(t!=null&&(t=""+Jt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Jt(a):""}function lf(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(o(92));if(Be(l)){if(1<l.length)throw Error(o(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Jt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function sn(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var H0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function nf(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||H0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function uf(e,t,a){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var u in t)l=t[u],t.hasOwnProperty(u)&&a[u]!==l&&nf(e,u,l)}else for(var c in t)t.hasOwnProperty(c)&&nf(e,c,t[c])}function so(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var B0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),q0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function xu(e){return q0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var fo=null;function ho(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var fn=null,dn=null;function rf(e){var t=je(e);if(t&&(e=t.stateNode)){var a=e[k]||null;e:switch(e=t.stateNode,t.type){case"input":if(oo(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Ft(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var u=l[k]||null;if(!u)throw Error(o(90));oo(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&ef(l)}break e;case"textarea":af(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&cn(e,!!a.multiple,t,!1)}}}var mo=!1;function of(e,t,a){if(mo)return e(t,a);mo=!0;try{var l=e(t);return l}finally{if(mo=!1,(fn!==null||dn!==null)&&(rr(),fn&&(t=fn,e=dn,dn=fn=null,rf(t),e)))for(t=0;t<e.length;t++)rf(e[t])}}function Pn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[k]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(o(231,t,typeof a));return a}var Ca=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),yo=!1;if(Ca)try{var In={};Object.defineProperty(In,"passive",{get:function(){yo=!0}}),window.addEventListener("test",In,In),window.removeEventListener("test",In,In)}catch{yo=!1}var Pa=null,po=null,Ru=null;function cf(){if(Ru)return Ru;var e,t=po,a=t.length,l,u="value"in Pa?Pa.value:Pa.textContent,c=u.length;for(e=0;e<a&&t[e]===u[e];e++);var h=a-e;for(l=1;l<=h&&t[a-l]===u[c-l];l++);return Ru=u.slice(e,1<l?1-l:void 0)}function Tu(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ou(){return!0}function sf(){return!1}function Nt(e){function t(a,l,u,c,h){this._reactName=a,this._targetInst=u,this.type=l,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(a=e[v],this[v]=a?a(c):c[v]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Ou:sf,this.isPropagationStopped=sf,this}return g(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ou)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ou)},persist:function(){},isPersistent:Ou}),t}var Cl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Au=Nt(Cl),ei=g({},Cl,{view:0,detail:0}),Y0=Nt(ei),vo,go,ti,wu=g({},ei,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:So,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ti&&(ti&&e.type==="mousemove"?(vo=e.screenX-ti.screenX,go=e.screenY-ti.screenY):go=vo=0,ti=e),vo)},movementY:function(e){return"movementY"in e?e.movementY:go}}),ff=Nt(wu),G0=g({},wu,{dataTransfer:0}),X0=Nt(G0),V0=g({},ei,{relatedTarget:0}),bo=Nt(V0),Q0=g({},Cl,{animationName:0,elapsedTime:0,pseudoElement:0}),Z0=Nt(Q0),K0=g({},Cl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),k0=Nt(K0),J0=g({},Cl,{data:0}),df=Nt(J0),F0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},W0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function P0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=W0[e])?!!t[e]:!1}function So(){return P0}var I0=g({},ei,{key:function(e){if(e.key){var t=F0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Tu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?$0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:So,charCode:function(e){return e.type==="keypress"?Tu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Tu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ep=Nt(I0),tp=g({},wu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hf=Nt(tp),ap=g({},ei,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:So}),lp=Nt(ap),np=g({},Cl,{propertyName:0,elapsedTime:0,pseudoElement:0}),ip=Nt(np),up=g({},wu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),rp=Nt(up),op=g({},Cl,{newState:0,oldState:0}),cp=Nt(op),sp=[9,13,27,32],Eo=Ca&&"CompositionEvent"in window,ai=null;Ca&&"documentMode"in document&&(ai=document.documentMode);var fp=Ca&&"TextEvent"in window&&!ai,mf=Ca&&(!Eo||ai&&8<ai&&11>=ai),yf=" ",pf=!1;function vf(e,t){switch(e){case"keyup":return sp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function gf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var hn=!1;function dp(e,t){switch(e){case"compositionend":return gf(t);case"keypress":return t.which!==32?null:(pf=!0,yf);case"textInput":return e=t.data,e===yf&&pf?null:e;default:return null}}function hp(e,t){if(hn)return e==="compositionend"||!Eo&&vf(e,t)?(e=cf(),Ru=po=Pa=null,hn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return mf&&t.locale!=="ko"?null:t.data;default:return null}}var mp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!mp[e.type]:t==="textarea"}function Sf(e,t,a,l){fn?dn?dn.push(l):dn=[l]:fn=l,t=hr(t,"onChange"),0<t.length&&(a=new Au("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var li=null,ni=null;function yp(e){em(e,0)}function Du(e){var t=$e(e);if(ef(t))return e}function Ef(e,t){if(e==="change")return t}var xf=!1;if(Ca){var xo;if(Ca){var Ro="oninput"in document;if(!Ro){var Rf=document.createElement("div");Rf.setAttribute("oninput","return;"),Ro=typeof Rf.oninput=="function"}xo=Ro}else xo=!1;xf=xo&&(!document.documentMode||9<document.documentMode)}function Tf(){li&&(li.detachEvent("onpropertychange",Of),ni=li=null)}function Of(e){if(e.propertyName==="value"&&Du(ni)){var t=[];Sf(t,ni,e,ho(e)),of(yp,t)}}function pp(e,t,a){e==="focusin"?(Tf(),li=t,ni=a,li.attachEvent("onpropertychange",Of)):e==="focusout"&&Tf()}function vp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Du(ni)}function gp(e,t){if(e==="click")return Du(t)}function bp(e,t){if(e==="input"||e==="change")return Du(t)}function Sp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qt=typeof Object.is=="function"?Object.is:Sp;function ii(e,t){if(qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var u=a[l];if(!ln.call(t,u)||!qt(e[u],t[u]))return!1}return!0}function Af(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wf(e,t){var a=Af(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Af(a)}}function Df(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Df(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Mf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Eu(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Eu(e.document)}return t}function To(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ep=Ca&&"documentMode"in document&&11>=document.documentMode,mn=null,Oo=null,ui=null,Ao=!1;function Nf(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Ao||mn==null||mn!==Eu(l)||(l=mn,"selectionStart"in l&&To(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ui&&ii(ui,l)||(ui=l,l=hr(Oo,"onSelect"),0<l.length&&(t=new Au("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=mn)))}function Ul(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var yn={animationend:Ul("Animation","AnimationEnd"),animationiteration:Ul("Animation","AnimationIteration"),animationstart:Ul("Animation","AnimationStart"),transitionrun:Ul("Transition","TransitionRun"),transitionstart:Ul("Transition","TransitionStart"),transitioncancel:Ul("Transition","TransitionCancel"),transitionend:Ul("Transition","TransitionEnd")},wo={},_f={};Ca&&(_f=document.createElement("div").style,"AnimationEvent"in window||(delete yn.animationend.animation,delete yn.animationiteration.animation,delete yn.animationstart.animation),"TransitionEvent"in window||delete yn.transitionend.transition);function Ll(e){if(wo[e])return wo[e];if(!yn[e])return e;var t=yn[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in _f)return wo[e]=t[a];return e}var zf=Ll("animationend"),Cf=Ll("animationiteration"),Uf=Ll("animationstart"),xp=Ll("transitionrun"),Rp=Ll("transitionstart"),Tp=Ll("transitioncancel"),Lf=Ll("transitionend"),jf=new Map,Do="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Do.push("scrollEnd");function oa(e,t){jf.set(e,t),Bt(t,[e])}var Hf=new WeakMap;function $t(e,t){if(typeof e=="object"&&e!==null){var a=Hf.get(e);return a!==void 0?a:(t={value:e,source:t,stack:bu(t)},Hf.set(e,t),t)}return{value:e,source:t,stack:bu(t)}}var Wt=[],pn=0,Mo=0;function Mu(){for(var e=pn,t=Mo=pn=0;t<e;){var a=Wt[t];Wt[t++]=null;var l=Wt[t];Wt[t++]=null;var u=Wt[t];Wt[t++]=null;var c=Wt[t];if(Wt[t++]=null,l!==null&&u!==null){var h=l.pending;h===null?u.next=u:(u.next=h.next,h.next=u),l.pending=u}c!==0&&Bf(a,u,c)}}function Nu(e,t,a,l){Wt[pn++]=e,Wt[pn++]=t,Wt[pn++]=a,Wt[pn++]=l,Mo|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function No(e,t,a,l){return Nu(e,t,a,l),_u(e)}function vn(e,t){return Nu(e,null,null,t),_u(e)}function Bf(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var u=!1,c=e.return;c!==null;)c.childLanes|=a,l=c.alternate,l!==null&&(l.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(u=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,u&&t!==null&&(u=31-gt(a),e=c.hiddenUpdates,l=e[u],l===null?e[u]=[t]:l.push(t),t.lane=a|536870912),c):null}function _u(e){if(50<_i)throw _i=0,jc=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var gn={};function Op(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Yt(e,t,a,l){return new Op(e,t,a,l)}function _o(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ua(e,t){var a=e.alternate;return a===null?(a=Yt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function qf(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function zu(e,t,a,l,u,c){var h=0;if(l=e,typeof e=="function")_o(e)&&(h=1);else if(typeof e=="string")h=wv(e,a,ue.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case pe:return e=Yt(31,a,t,u),e.elementType=pe,e.lanes=c,e;case U:return jl(a.children,u,c,t);case Q:h=8,u|=24;break;case H:return e=Yt(12,a,t,u|2),e.elementType=H,e.lanes=c,e;case $:return e=Yt(13,a,t,u),e.elementType=$,e.lanes=c,e;case _:return e=Yt(19,a,t,u),e.elementType=_,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case K:case F:h=10;break e;case J:h=9;break e;case ye:h=11;break e;case ce:h=14;break e;case ge:h=16,l=null;break e}h=29,a=Error(o(130,e===null?"null":typeof e,"")),l=null}return t=Yt(h,a,t,u),t.elementType=e,t.type=l,t.lanes=c,t}function jl(e,t,a,l){return e=Yt(7,e,l,t),e.lanes=a,e}function zo(e,t,a){return e=Yt(6,e,null,t),e.lanes=a,e}function Co(e,t,a){return t=Yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var bn=[],Sn=0,Cu=null,Uu=0,Pt=[],It=0,Hl=null,La=1,ja="";function Bl(e,t){bn[Sn++]=Uu,bn[Sn++]=Cu,Cu=e,Uu=t}function Yf(e,t,a){Pt[It++]=La,Pt[It++]=ja,Pt[It++]=Hl,Hl=e;var l=La;e=ja;var u=32-gt(l)-1;l&=~(1<<u),a+=1;var c=32-gt(t)+u;if(30<c){var h=u-u%5;c=(l&(1<<h)-1).toString(32),l>>=h,u-=h,La=1<<32-gt(t)+u|a<<u|l,ja=c+e}else La=1<<c|a<<u|l,ja=e}function Uo(e){e.return!==null&&(Bl(e,1),Yf(e,1,0))}function Lo(e){for(;e===Cu;)Cu=bn[--Sn],bn[Sn]=null,Uu=bn[--Sn],bn[Sn]=null;for(;e===Hl;)Hl=Pt[--It],Pt[It]=null,ja=Pt[--It],Pt[It]=null,La=Pt[--It],Pt[It]=null}var wt=null,lt=null,He=!1,ql=null,va=!1,jo=Error(o(519));function Yl(e){var t=Error(o(418,""));throw ci($t(t,e)),jo}function Gf(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Z]=e,t[k]=l,a){case"dialog":Me("cancel",t),Me("close",t);break;case"iframe":case"object":case"embed":Me("load",t);break;case"video":case"audio":for(a=0;a<Ci.length;a++)Me(Ci[a],t);break;case"source":Me("error",t);break;case"img":case"image":case"link":Me("error",t),Me("load",t);break;case"details":Me("toggle",t);break;case"input":Me("invalid",t),tf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Su(t);break;case"select":Me("invalid",t);break;case"textarea":Me("invalid",t),lf(t,l.value,l.defaultValue,l.children),Su(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||nm(t.textContent,a)?(l.popover!=null&&(Me("beforetoggle",t),Me("toggle",t)),l.onScroll!=null&&Me("scroll",t),l.onScrollEnd!=null&&Me("scrollend",t),l.onClick!=null&&(t.onclick=mr),t=!0):t=!1,t||Yl(e)}function Xf(e){for(wt=e.return;wt;)switch(wt.tag){case 5:case 13:va=!1;return;case 27:case 3:va=!0;return;default:wt=wt.return}}function ri(e){if(e!==wt)return!1;if(!He)return Xf(e),He=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Pc(e.type,e.memoizedProps)),a=!a),a&&lt&&Yl(e),Xf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){lt=sa(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}lt=null}}else t===27?(t=lt,ml(e.type)?(e=as,as=null,lt=e):lt=t):lt=wt?sa(e.stateNode.nextSibling):null;return!0}function oi(){lt=wt=null,He=!1}function Vf(){var e=ql;return e!==null&&(Ct===null?Ct=e:Ct.push.apply(Ct,e),ql=null),e}function ci(e){ql===null?ql=[e]:ql.push(e)}var Ho=X(null),Gl=null,Ha=null;function Ia(e,t,a){P(Ho,t._currentValue),t._currentValue=a}function Ba(e){e._currentValue=Ho.current,I(Ho)}function Bo(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function qo(e,t,a,l){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var c=u.dependencies;if(c!==null){var h=u.child;c=c.firstContext;e:for(;c!==null;){var v=c;c=u;for(var S=0;S<t.length;S++)if(v.context===t[S]){c.lanes|=a,v=c.alternate,v!==null&&(v.lanes|=a),Bo(c.return,a,e),l||(h=null);break e}c=v.next}}else if(u.tag===18){if(h=u.return,h===null)throw Error(o(341));h.lanes|=a,c=h.alternate,c!==null&&(c.lanes|=a),Bo(h,a,e),h=null}else h=u.child;if(h!==null)h.return=u;else for(h=u;h!==null;){if(h===e){h=null;break}if(u=h.sibling,u!==null){u.return=h.return,h=u;break}h=h.return}u=h}}function si(e,t,a,l){e=null;for(var u=t,c=!1;u!==null;){if(!c){if((u.flags&524288)!==0)c=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var h=u.alternate;if(h===null)throw Error(o(387));if(h=h.memoizedProps,h!==null){var v=u.type;qt(u.pendingProps.value,h.value)||(e!==null?e.push(v):e=[v])}}else if(u===Ot.current){if(h=u.alternate,h===null)throw Error(o(387));h.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(qi):e=[qi])}u=u.return}e!==null&&qo(t,e,a,l),t.flags|=262144}function Lu(e){for(e=e.firstContext;e!==null;){if(!qt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Xl(e){Gl=e,Ha=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function xt(e){return Qf(Gl,e)}function ju(e,t){return Gl===null&&Xl(e),Qf(e,t)}function Qf(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Ha===null){if(e===null)throw Error(o(308));Ha=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Ha=Ha.next=t;return a}var Ap=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},wp=n.unstable_scheduleCallback,Dp=n.unstable_NormalPriority,ht={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Yo(){return{controller:new Ap,data:new Map,refCount:0}}function fi(e){e.refCount--,e.refCount===0&&wp(Dp,function(){e.controller.abort()})}var di=null,Go=0,En=0,xn=null;function Mp(e,t){if(di===null){var a=di=[];Go=0,En=Vc(),xn={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Go++,t.then(Zf,Zf),t}function Zf(){if(--Go===0&&di!==null){xn!==null&&(xn.status="fulfilled");var e=di;di=null,En=0,xn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Np(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(u){a.push(u)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var u=0;u<a.length;u++)(0,a[u])(t)},function(u){for(l.status="rejected",l.reason=u,u=0;u<a.length;u++)(0,a[u])(void 0)}),l}var Kf=C.S;C.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Mp(e,t),Kf!==null&&Kf(e,t)};var Vl=X(null);function Xo(){var e=Vl.current;return e!==null?e:Je.pooledCache}function Hu(e,t){t===null?P(Vl,Vl.current):P(Vl,t.pool)}function kf(){var e=Xo();return e===null?null:{parent:ht._currentValue,pool:e}}var hi=Error(o(460)),Jf=Error(o(474)),Bu=Error(o(542)),Vo={then:function(){}};function Ff(e){return e=e.status,e==="fulfilled"||e==="rejected"}function qu(){}function $f(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(qu,qu),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Pf(e),e;default:if(typeof t.status=="string")t.then(qu,qu);else{if(e=Je,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=l}},function(l){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Pf(e),e}throw mi=t,hi}}var mi=null;function Wf(){if(mi===null)throw Error(o(459));var e=mi;return mi=null,e}function Pf(e){if(e===hi||e===Bu)throw Error(o(483))}var el=!1;function Qo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Zo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function tl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function al(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(qe&2)!==0){var u=l.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),l.pending=t,t=_u(e),Bf(e,null,a),t}return Nu(e,l,t,a),_u(e)}function yi(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,zl(e,a)}}function Ko(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var u=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var h={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?u=c=h:c=c.next=h,a=a.next}while(a!==null);c===null?u=c=t:c=c.next=t}else u=c=t;a={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var ko=!1;function pi(){if(ko){var e=xn;if(e!==null)throw e}}function vi(e,t,a,l){ko=!1;var u=e.updateQueue;el=!1;var c=u.firstBaseUpdate,h=u.lastBaseUpdate,v=u.shared.pending;if(v!==null){u.shared.pending=null;var S=v,N=S.next;S.next=null,h===null?c=N:h.next=N,h=S;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,v=Y.lastBaseUpdate,v!==h&&(v===null?Y.firstBaseUpdate=N:v.next=N,Y.lastBaseUpdate=S))}if(c!==null){var V=u.baseState;h=0,Y=N=S=null,v=c;do{var z=v.lane&-536870913,L=z!==v.lane;if(L?(_e&z)===z:(l&z)===z){z!==0&&z===En&&(ko=!0),Y!==null&&(Y=Y.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});e:{var ve=e,de=v;z=t;var Qe=a;switch(de.tag){case 1:if(ve=de.payload,typeof ve=="function"){V=ve.call(Qe,V,z);break e}V=ve;break e;case 3:ve.flags=ve.flags&-65537|128;case 0:if(ve=de.payload,z=typeof ve=="function"?ve.call(Qe,V,z):ve,z==null)break e;V=g({},V,z);break e;case 2:el=!0}}z=v.callback,z!==null&&(e.flags|=64,L&&(e.flags|=8192),L=u.callbacks,L===null?u.callbacks=[z]:L.push(z))}else L={lane:z,tag:v.tag,payload:v.payload,callback:v.callback,next:null},Y===null?(N=Y=L,S=V):Y=Y.next=L,h|=z;if(v=v.next,v===null){if(v=u.shared.pending,v===null)break;L=v,v=L.next,L.next=null,u.lastBaseUpdate=L,u.shared.pending=null}}while(!0);Y===null&&(S=V),u.baseState=S,u.firstBaseUpdate=N,u.lastBaseUpdate=Y,c===null&&(u.shared.lanes=0),sl|=h,e.lanes=h,e.memoizedState=V}}function If(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function ed(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)If(a[e],t)}var Rn=X(null),Yu=X(0);function td(e,t){e=Za,P(Yu,e),P(Rn,t),Za=e|t.baseLanes}function Jo(){P(Yu,Za),P(Rn,Rn.current)}function Fo(){Za=Yu.current,I(Rn),I(Yu)}var ll=0,Oe=null,Xe=null,st=null,Gu=!1,Tn=!1,Ql=!1,Xu=0,gi=0,On=null,_p=0;function it(){throw Error(o(321))}function $o(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!qt(e[a],t[a]))return!1;return!0}function Wo(e,t,a,l,u,c){return ll=c,Oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,C.H=e===null||e.memoizedState===null?Bd:qd,Ql=!1,c=a(l,u),Ql=!1,Tn&&(c=ld(t,a,l,u)),ad(e),c}function ad(e){C.H=Ju;var t=Xe!==null&&Xe.next!==null;if(ll=0,st=Xe=Oe=null,Gu=!1,gi=0,On=null,t)throw Error(o(300));e===null||yt||(e=e.dependencies,e!==null&&Lu(e)&&(yt=!0))}function ld(e,t,a,l){Oe=e;var u=0;do{if(Tn&&(On=null),gi=0,Tn=!1,25<=u)throw Error(o(301));if(u+=1,st=Xe=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}C.H=Bp,c=t(a,l)}while(Tn);return c}function zp(){var e=C.H,t=e.useState()[0];return t=typeof t.then=="function"?bi(t):t,e=e.useState()[0],(Xe!==null?Xe.memoizedState:null)!==e&&(Oe.flags|=1024),t}function Po(){var e=Xu!==0;return Xu=0,e}function Io(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function ec(e){if(Gu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Gu=!1}ll=0,st=Xe=Oe=null,Tn=!1,gi=Xu=0,On=null}function _t(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return st===null?Oe.memoizedState=st=e:st=st.next=e,st}function ft(){if(Xe===null){var e=Oe.alternate;e=e!==null?e.memoizedState:null}else e=Xe.next;var t=st===null?Oe.memoizedState:st.next;if(t!==null)st=t,Xe=e;else{if(e===null)throw Oe.alternate===null?Error(o(467)):Error(o(310));Xe=e,e={memoizedState:Xe.memoizedState,baseState:Xe.baseState,baseQueue:Xe.baseQueue,queue:Xe.queue,next:null},st===null?Oe.memoizedState=st=e:st=st.next=e}return st}function tc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function bi(e){var t=gi;return gi+=1,On===null&&(On=[]),e=$f(On,e,t),t=Oe,(st===null?t.memoizedState:st.next)===null&&(t=t.alternate,C.H=t===null||t.memoizedState===null?Bd:qd),e}function Vu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return bi(e);if(e.$$typeof===F)return xt(e)}throw Error(o(438,String(e)))}function ac(e){var t=null,a=Oe.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=Oe.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=tc(),Oe.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Ue;return t.index++,a}function qa(e,t){return typeof t=="function"?t(e):t}function Qu(e){var t=ft();return lc(t,Xe,e)}function lc(e,t,a){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=a;var u=e.baseQueue,c=l.pending;if(c!==null){if(u!==null){var h=u.next;u.next=c.next,c.next=h}t.baseQueue=u=c,l.pending=null}if(c=e.baseState,u===null)e.memoizedState=c;else{t=u.next;var v=h=null,S=null,N=t,Y=!1;do{var V=N.lane&-536870913;if(V!==N.lane?(_e&V)===V:(ll&V)===V){var z=N.revertLane;if(z===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),V===En&&(Y=!0);else if((ll&z)===z){N=N.next,z===En&&(Y=!0);continue}else V={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},S===null?(v=S=V,h=c):S=S.next=V,Oe.lanes|=z,sl|=z;V=N.action,Ql&&a(c,V),c=N.hasEagerState?N.eagerState:a(c,V)}else z={lane:V,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},S===null?(v=S=z,h=c):S=S.next=z,Oe.lanes|=V,sl|=V;N=N.next}while(N!==null&&N!==t);if(S===null?h=c:S.next=v,!qt(c,e.memoizedState)&&(yt=!0,Y&&(a=xn,a!==null)))throw a;e.memoizedState=c,e.baseState=h,e.baseQueue=S,l.lastRenderedState=c}return u===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function nc(e){var t=ft(),a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=e;var l=a.dispatch,u=a.pending,c=t.memoizedState;if(u!==null){a.pending=null;var h=u=u.next;do c=e(c,h.action),h=h.next;while(h!==u);qt(c,t.memoizedState)||(yt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,l]}function nd(e,t,a){var l=Oe,u=ft(),c=He;if(c){if(a===void 0)throw Error(o(407));a=a()}else a=t();var h=!qt((Xe||u).memoizedState,a);h&&(u.memoizedState=a,yt=!0),u=u.queue;var v=rd.bind(null,l,u,e);if(Si(2048,8,v,[e]),u.getSnapshot!==t||h||st!==null&&st.memoizedState.tag&1){if(l.flags|=2048,An(9,Zu(),ud.bind(null,l,u,a,t),null),Je===null)throw Error(o(349));c||(ll&124)!==0||id(l,t,a)}return a}function id(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Oe.updateQueue,t===null?(t=tc(),Oe.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function ud(e,t,a,l){t.value=a,t.getSnapshot=l,od(t)&&cd(e)}function rd(e,t,a){return a(function(){od(t)&&cd(e)})}function od(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!qt(e,a)}catch{return!0}}function cd(e){var t=vn(e,2);t!==null&&Zt(t,e,2)}function ic(e){var t=_t();if(typeof e=="function"){var a=e;if(e=a(),Ql){ra(!0);try{a()}finally{ra(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qa,lastRenderedState:e},t}function sd(e,t,a,l){return e.baseState=a,lc(e,Xe,typeof l=="function"?l:qa)}function Cp(e,t,a,l,u){if(ku(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){c.listeners.push(h)}};C.T!==null?a(!0):c.isTransition=!1,l(c),a=t.pending,a===null?(c.next=t.pending=c,fd(t,c)):(c.next=a.next,t.pending=a.next=c)}}function fd(e,t){var a=t.action,l=t.payload,u=e.state;if(t.isTransition){var c=C.T,h={};C.T=h;try{var v=a(u,l),S=C.S;S!==null&&S(h,v),dd(e,t,v)}catch(N){uc(e,t,N)}finally{C.T=c}}else try{c=a(u,l),dd(e,t,c)}catch(N){uc(e,t,N)}}function dd(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){hd(e,t,l)},function(l){return uc(e,t,l)}):hd(e,t,a)}function hd(e,t,a){t.status="fulfilled",t.value=a,md(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,fd(e,a)))}function uc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,md(t),t=t.next;while(t!==l)}e.action=null}function md(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function yd(e,t){return t}function pd(e,t){if(He){var a=Je.formState;if(a!==null){e:{var l=Oe;if(He){if(lt){t:{for(var u=lt,c=va;u.nodeType!==8;){if(!c){u=null;break t}if(u=sa(u.nextSibling),u===null){u=null;break t}}c=u.data,u=c==="F!"||c==="F"?u:null}if(u){lt=sa(u.nextSibling),l=u.data==="F!";break e}}Yl(l)}l=!1}l&&(t=a[0])}}return a=_t(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yd,lastRenderedState:t},a.queue=l,a=Ld.bind(null,Oe,l),l.dispatch=a,l=ic(!1),c=fc.bind(null,Oe,!1,l.queue),l=_t(),u={state:t,dispatch:null,action:e,pending:null},l.queue=u,a=Cp.bind(null,Oe,u,c,a),u.dispatch=a,l.memoizedState=e,[t,a,!1]}function vd(e){var t=ft();return gd(t,Xe,e)}function gd(e,t,a){if(t=lc(e,t,yd)[0],e=Qu(qa)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=bi(t)}catch(h){throw h===hi?Bu:h}else l=t;t=ft();var u=t.queue,c=u.dispatch;return a!==t.memoizedState&&(Oe.flags|=2048,An(9,Zu(),Up.bind(null,u,a),null)),[l,c,e]}function Up(e,t){e.action=t}function bd(e){var t=ft(),a=Xe;if(a!==null)return gd(t,a,e);ft(),t=t.memoizedState,a=ft();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function An(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=Oe.updateQueue,t===null&&(t=tc(),Oe.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Zu(){return{destroy:void 0,resource:void 0}}function Sd(){return ft().memoizedState}function Ku(e,t,a,l){var u=_t();l=l===void 0?null:l,Oe.flags|=e,u.memoizedState=An(1|t,Zu(),a,l)}function Si(e,t,a,l){var u=ft();l=l===void 0?null:l;var c=u.memoizedState.inst;Xe!==null&&l!==null&&$o(l,Xe.memoizedState.deps)?u.memoizedState=An(t,c,a,l):(Oe.flags|=e,u.memoizedState=An(1|t,c,a,l))}function Ed(e,t){Ku(8390656,8,e,t)}function xd(e,t){Si(2048,8,e,t)}function Rd(e,t){return Si(4,2,e,t)}function Td(e,t){return Si(4,4,e,t)}function Od(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ad(e,t,a){a=a!=null?a.concat([e]):null,Si(4,4,Od.bind(null,t,e),a)}function rc(){}function wd(e,t){var a=ft();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&$o(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Dd(e,t){var a=ft();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&$o(t,l[1]))return l[0];if(l=e(),Ql){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l}function oc(e,t,a){return a===void 0||(ll&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=_h(),Oe.lanes|=e,sl|=e,a)}function Md(e,t,a,l){return qt(a,t)?a:Rn.current!==null?(e=oc(e,a,l),qt(e,t)||(yt=!0),e):(ll&42)===0?(yt=!0,e.memoizedState=a):(e=_h(),Oe.lanes|=e,sl|=e,t)}function Nd(e,t,a,l,u){var c=W.p;W.p=c!==0&&8>c?c:8;var h=C.T,v={};C.T=v,fc(e,!1,t,a);try{var S=u(),N=C.S;if(N!==null&&N(v,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var Y=Np(S,l);Ei(e,t,Y,Qt(e))}else Ei(e,t,l,Qt(e))}catch(V){Ei(e,t,{then:function(){},status:"rejected",reason:V},Qt())}finally{W.p=c,C.T=h}}function Lp(){}function cc(e,t,a,l){if(e.tag!==5)throw Error(o(476));var u=_d(e).queue;Nd(e,u,t,ee,a===null?Lp:function(){return zd(e),a(l)})}function _d(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ee,baseState:ee,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qa,lastRenderedState:ee},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qa,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function zd(e){var t=_d(e).next.queue;Ei(e,t,{},Qt())}function sc(){return xt(qi)}function Cd(){return ft().memoizedState}function Ud(){return ft().memoizedState}function jp(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Qt();e=tl(a);var l=al(t,e,a);l!==null&&(Zt(l,t,a),yi(l,t,a)),t={cache:Yo()},e.payload=t;return}t=t.return}}function Hp(e,t,a){var l=Qt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ku(e)?jd(t,a):(a=No(e,t,a,l),a!==null&&(Zt(a,e,l),Hd(a,t,l)))}function Ld(e,t,a){var l=Qt();Ei(e,t,a,l)}function Ei(e,t,a,l){var u={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ku(e))jd(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,v=c(h,a);if(u.hasEagerState=!0,u.eagerState=v,qt(v,h))return Nu(e,t,u,0),Je===null&&Mu(),!1}catch{}finally{}if(a=No(e,t,u,l),a!==null)return Zt(a,e,l),Hd(a,t,l),!0}return!1}function fc(e,t,a,l){if(l={lane:2,revertLane:Vc(),action:l,hasEagerState:!1,eagerState:null,next:null},ku(e)){if(t)throw Error(o(479))}else t=No(e,a,l,2),t!==null&&Zt(t,e,2)}function ku(e){var t=e.alternate;return e===Oe||t!==null&&t===Oe}function jd(e,t){Tn=Gu=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Hd(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,zl(e,a)}}var Ju={readContext:xt,use:Vu,useCallback:it,useContext:it,useEffect:it,useImperativeHandle:it,useLayoutEffect:it,useInsertionEffect:it,useMemo:it,useReducer:it,useRef:it,useState:it,useDebugValue:it,useDeferredValue:it,useTransition:it,useSyncExternalStore:it,useId:it,useHostTransitionStatus:it,useFormState:it,useActionState:it,useOptimistic:it,useMemoCache:it,useCacheRefresh:it},Bd={readContext:xt,use:Vu,useCallback:function(e,t){return _t().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:Ed,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ku(4194308,4,Od.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ku(4194308,4,e,t)},useInsertionEffect:function(e,t){Ku(4,2,e,t)},useMemo:function(e,t){var a=_t();t=t===void 0?null:t;var l=e();if(Ql){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=_t();if(a!==void 0){var u=a(t);if(Ql){ra(!0);try{a(t)}finally{ra(!1)}}}else u=t;return l.memoizedState=l.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},l.queue=e,e=e.dispatch=Hp.bind(null,Oe,e),[l.memoizedState,e]},useRef:function(e){var t=_t();return e={current:e},t.memoizedState=e},useState:function(e){e=ic(e);var t=e.queue,a=Ld.bind(null,Oe,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:rc,useDeferredValue:function(e,t){var a=_t();return oc(a,e,t)},useTransition:function(){var e=ic(!1);return e=Nd.bind(null,Oe,e.queue,!0,!1),_t().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=Oe,u=_t();if(He){if(a===void 0)throw Error(o(407));a=a()}else{if(a=t(),Je===null)throw Error(o(349));(_e&124)!==0||id(l,t,a)}u.memoizedState=a;var c={value:a,getSnapshot:t};return u.queue=c,Ed(rd.bind(null,l,c,e),[e]),l.flags|=2048,An(9,Zu(),ud.bind(null,l,c,a,t),null),a},useId:function(){var e=_t(),t=Je.identifierPrefix;if(He){var a=ja,l=La;a=(l&~(1<<32-gt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Xu++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=_p++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:sc,useFormState:pd,useActionState:pd,useOptimistic:function(e){var t=_t();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=fc.bind(null,Oe,!0,a),a.dispatch=t,[e,t]},useMemoCache:ac,useCacheRefresh:function(){return _t().memoizedState=jp.bind(null,Oe)}},qd={readContext:xt,use:Vu,useCallback:wd,useContext:xt,useEffect:xd,useImperativeHandle:Ad,useInsertionEffect:Rd,useLayoutEffect:Td,useMemo:Dd,useReducer:Qu,useRef:Sd,useState:function(){return Qu(qa)},useDebugValue:rc,useDeferredValue:function(e,t){var a=ft();return Md(a,Xe.memoizedState,e,t)},useTransition:function(){var e=Qu(qa)[0],t=ft().memoizedState;return[typeof e=="boolean"?e:bi(e),t]},useSyncExternalStore:nd,useId:Cd,useHostTransitionStatus:sc,useFormState:vd,useActionState:vd,useOptimistic:function(e,t){var a=ft();return sd(a,Xe,e,t)},useMemoCache:ac,useCacheRefresh:Ud},Bp={readContext:xt,use:Vu,useCallback:wd,useContext:xt,useEffect:xd,useImperativeHandle:Ad,useInsertionEffect:Rd,useLayoutEffect:Td,useMemo:Dd,useReducer:nc,useRef:Sd,useState:function(){return nc(qa)},useDebugValue:rc,useDeferredValue:function(e,t){var a=ft();return Xe===null?oc(a,e,t):Md(a,Xe.memoizedState,e,t)},useTransition:function(){var e=nc(qa)[0],t=ft().memoizedState;return[typeof e=="boolean"?e:bi(e),t]},useSyncExternalStore:nd,useId:Cd,useHostTransitionStatus:sc,useFormState:bd,useActionState:bd,useOptimistic:function(e,t){var a=ft();return Xe!==null?sd(a,Xe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:ac,useCacheRefresh:Ud},wn=null,xi=0;function Fu(e){var t=xi;return xi+=1,wn===null&&(wn=[]),$f(wn,e,t)}function Ri(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function $u(e,t){throw t.$$typeof===b?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Yd(e){var t=e._init;return t(e._payload)}function Gd(e){function t(A,R){if(e){var D=A.deletions;D===null?(A.deletions=[R],A.flags|=16):D.push(R)}}function a(A,R){if(!e)return null;for(;R!==null;)t(A,R),R=R.sibling;return null}function l(A){for(var R=new Map;A!==null;)A.key!==null?R.set(A.key,A):R.set(A.index,A),A=A.sibling;return R}function u(A,R){return A=Ua(A,R),A.index=0,A.sibling=null,A}function c(A,R,D){return A.index=D,e?(D=A.alternate,D!==null?(D=D.index,D<R?(A.flags|=67108866,R):D):(A.flags|=67108866,R)):(A.flags|=1048576,R)}function h(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function v(A,R,D,G){return R===null||R.tag!==6?(R=zo(D,A.mode,G),R.return=A,R):(R=u(R,D),R.return=A,R)}function S(A,R,D,G){var te=D.type;return te===U?Y(A,R,D.props.children,G,D.key):R!==null&&(R.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===ge&&Yd(te)===R.type)?(R=u(R,D.props),Ri(R,D),R.return=A,R):(R=zu(D.type,D.key,D.props,null,A.mode,G),Ri(R,D),R.return=A,R)}function N(A,R,D,G){return R===null||R.tag!==4||R.stateNode.containerInfo!==D.containerInfo||R.stateNode.implementation!==D.implementation?(R=Co(D,A.mode,G),R.return=A,R):(R=u(R,D.children||[]),R.return=A,R)}function Y(A,R,D,G,te){return R===null||R.tag!==7?(R=jl(D,A.mode,G,te),R.return=A,R):(R=u(R,D),R.return=A,R)}function V(A,R,D){if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return R=zo(""+R,A.mode,D),R.return=A,R;if(typeof R=="object"&&R!==null){switch(R.$$typeof){case T:return D=zu(R.type,R.key,R.props,null,A.mode,D),Ri(D,R),D.return=A,D;case M:return R=Co(R,A.mode,D),R.return=A,R;case ge:var G=R._init;return R=G(R._payload),V(A,R,D)}if(Be(R)||Fe(R))return R=jl(R,A.mode,D,null),R.return=A,R;if(typeof R.then=="function")return V(A,Fu(R),D);if(R.$$typeof===F)return V(A,ju(A,R),D);$u(A,R)}return null}function z(A,R,D,G){var te=R!==null?R.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return te!==null?null:v(A,R,""+D,G);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case T:return D.key===te?S(A,R,D,G):null;case M:return D.key===te?N(A,R,D,G):null;case ge:return te=D._init,D=te(D._payload),z(A,R,D,G)}if(Be(D)||Fe(D))return te!==null?null:Y(A,R,D,G,null);if(typeof D.then=="function")return z(A,R,Fu(D),G);if(D.$$typeof===F)return z(A,R,ju(A,D),G);$u(A,D)}return null}function L(A,R,D,G,te){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return A=A.get(D)||null,v(R,A,""+G,te);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case T:return A=A.get(G.key===null?D:G.key)||null,S(R,A,G,te);case M:return A=A.get(G.key===null?D:G.key)||null,N(R,A,G,te);case ge:var Ae=G._init;return G=Ae(G._payload),L(A,R,D,G,te)}if(Be(G)||Fe(G))return A=A.get(D)||null,Y(R,A,G,te,null);if(typeof G.then=="function")return L(A,R,D,Fu(G),te);if(G.$$typeof===F)return L(A,R,D,ju(R,G),te);$u(R,G)}return null}function ve(A,R,D,G){for(var te=null,Ae=null,oe=R,me=R=0,vt=null;oe!==null&&me<D.length;me++){oe.index>me?(vt=oe,oe=null):vt=oe.sibling;var Ce=z(A,oe,D[me],G);if(Ce===null){oe===null&&(oe=vt);break}e&&oe&&Ce.alternate===null&&t(A,oe),R=c(Ce,R,me),Ae===null?te=Ce:Ae.sibling=Ce,Ae=Ce,oe=vt}if(me===D.length)return a(A,oe),He&&Bl(A,me),te;if(oe===null){for(;me<D.length;me++)oe=V(A,D[me],G),oe!==null&&(R=c(oe,R,me),Ae===null?te=oe:Ae.sibling=oe,Ae=oe);return He&&Bl(A,me),te}for(oe=l(oe);me<D.length;me++)vt=L(oe,A,me,D[me],G),vt!==null&&(e&&vt.alternate!==null&&oe.delete(vt.key===null?me:vt.key),R=c(vt,R,me),Ae===null?te=vt:Ae.sibling=vt,Ae=vt);return e&&oe.forEach(function(bl){return t(A,bl)}),He&&Bl(A,me),te}function de(A,R,D,G){if(D==null)throw Error(o(151));for(var te=null,Ae=null,oe=R,me=R=0,vt=null,Ce=D.next();oe!==null&&!Ce.done;me++,Ce=D.next()){oe.index>me?(vt=oe,oe=null):vt=oe.sibling;var bl=z(A,oe,Ce.value,G);if(bl===null){oe===null&&(oe=vt);break}e&&oe&&bl.alternate===null&&t(A,oe),R=c(bl,R,me),Ae===null?te=bl:Ae.sibling=bl,Ae=bl,oe=vt}if(Ce.done)return a(A,oe),He&&Bl(A,me),te;if(oe===null){for(;!Ce.done;me++,Ce=D.next())Ce=V(A,Ce.value,G),Ce!==null&&(R=c(Ce,R,me),Ae===null?te=Ce:Ae.sibling=Ce,Ae=Ce);return He&&Bl(A,me),te}for(oe=l(oe);!Ce.done;me++,Ce=D.next())Ce=L(oe,A,me,Ce.value,G),Ce!==null&&(e&&Ce.alternate!==null&&oe.delete(Ce.key===null?me:Ce.key),R=c(Ce,R,me),Ae===null?te=Ce:Ae.sibling=Ce,Ae=Ce);return e&&oe.forEach(function(qv){return t(A,qv)}),He&&Bl(A,me),te}function Qe(A,R,D,G){if(typeof D=="object"&&D!==null&&D.type===U&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case T:e:{for(var te=D.key;R!==null;){if(R.key===te){if(te=D.type,te===U){if(R.tag===7){a(A,R.sibling),G=u(R,D.props.children),G.return=A,A=G;break e}}else if(R.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===ge&&Yd(te)===R.type){a(A,R.sibling),G=u(R,D.props),Ri(G,D),G.return=A,A=G;break e}a(A,R);break}else t(A,R);R=R.sibling}D.type===U?(G=jl(D.props.children,A.mode,G,D.key),G.return=A,A=G):(G=zu(D.type,D.key,D.props,null,A.mode,G),Ri(G,D),G.return=A,A=G)}return h(A);case M:e:{for(te=D.key;R!==null;){if(R.key===te)if(R.tag===4&&R.stateNode.containerInfo===D.containerInfo&&R.stateNode.implementation===D.implementation){a(A,R.sibling),G=u(R,D.children||[]),G.return=A,A=G;break e}else{a(A,R);break}else t(A,R);R=R.sibling}G=Co(D,A.mode,G),G.return=A,A=G}return h(A);case ge:return te=D._init,D=te(D._payload),Qe(A,R,D,G)}if(Be(D))return ve(A,R,D,G);if(Fe(D)){if(te=Fe(D),typeof te!="function")throw Error(o(150));return D=te.call(D),de(A,R,D,G)}if(typeof D.then=="function")return Qe(A,R,Fu(D),G);if(D.$$typeof===F)return Qe(A,R,ju(A,D),G);$u(A,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,R!==null&&R.tag===6?(a(A,R.sibling),G=u(R,D),G.return=A,A=G):(a(A,R),G=zo(D,A.mode,G),G.return=A,A=G),h(A)):a(A,R)}return function(A,R,D,G){try{xi=0;var te=Qe(A,R,D,G);return wn=null,te}catch(oe){if(oe===hi||oe===Bu)throw oe;var Ae=Yt(29,oe,null,A.mode);return Ae.lanes=G,Ae.return=A,Ae}finally{}}}var Dn=Gd(!0),Xd=Gd(!1),ea=X(null),ga=null;function nl(e){var t=e.alternate;P(mt,mt.current&1),P(ea,e),ga===null&&(t===null||Rn.current!==null||t.memoizedState!==null)&&(ga=e)}function Vd(e){if(e.tag===22){if(P(mt,mt.current),P(ea,e),ga===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(ga=e)}}else il()}function il(){P(mt,mt.current),P(ea,ea.current)}function Ya(e){I(ea),ga===e&&(ga=null),I(mt)}var mt=X(0);function Wu(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||ts(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function dc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:g({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var hc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=Qt(),u=tl(l);u.payload=t,a!=null&&(u.callback=a),t=al(e,u,l),t!==null&&(Zt(t,e,l),yi(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=Qt(),u=tl(l);u.tag=1,u.payload=t,a!=null&&(u.callback=a),t=al(e,u,l),t!==null&&(Zt(t,e,l),yi(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Qt(),l=tl(a);l.tag=2,t!=null&&(l.callback=t),t=al(e,l,a),t!==null&&(Zt(t,e,a),yi(t,e,a))}};function Qd(e,t,a,l,u,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,h):t.prototype&&t.prototype.isPureReactComponent?!ii(a,l)||!ii(u,c):!0}function Zd(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&hc.enqueueReplaceState(t,t.state,null)}function Zl(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=g({},a));for(var u in e)a[u]===void 0&&(a[u]=e[u])}return a}var Pu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Kd(e){Pu(e)}function kd(e){console.error(e)}function Jd(e){Pu(e)}function Iu(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Fd(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function mc(e,t,a){return a=tl(a),a.tag=3,a.payload={element:null},a.callback=function(){Iu(e,t)},a}function $d(e){return e=tl(e),e.tag=3,e}function Wd(e,t,a,l){var u=a.type.getDerivedStateFromError;if(typeof u=="function"){var c=l.value;e.payload=function(){return u(c)},e.callback=function(){Fd(t,a,l)}}var h=a.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Fd(t,a,l),typeof u!="function"&&(fl===null?fl=new Set([this]):fl.add(this));var v=l.stack;this.componentDidCatch(l.value,{componentStack:v!==null?v:""})})}function qp(e,t,a,l,u){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&si(t,a,u,!0),a=ea.current,a!==null){switch(a.tag){case 13:return ga===null?Bc():a.alternate===null&&nt===0&&(nt=3),a.flags&=-257,a.flags|=65536,a.lanes=u,l===Vo?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Yc(e,l,u)),!1;case 22:return a.flags|=65536,l===Vo?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Yc(e,l,u)),!1}throw Error(o(435,a.tag))}return Yc(e,l,u),Bc(),!1}if(He)return t=ea.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,l!==jo&&(e=Error(o(422),{cause:l}),ci($t(e,a)))):(l!==jo&&(t=Error(o(423),{cause:l}),ci($t(t,a))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,l=$t(l,a),u=mc(e.stateNode,l,u),Ko(e,u),nt!==4&&(nt=2)),!1;var c=Error(o(520),{cause:l});if(c=$t(c,a),Ni===null?Ni=[c]:Ni.push(c),nt!==4&&(nt=2),t===null)return!0;l=$t(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=u&-u,a.lanes|=e,e=mc(a.stateNode,l,e),Ko(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(fl===null||!fl.has(c))))return a.flags|=65536,u&=-u,a.lanes|=u,u=$d(u),Wd(u,e,a,l),Ko(a,u),!1}a=a.return}while(a!==null);return!1}var Pd=Error(o(461)),yt=!1;function bt(e,t,a,l){t.child=e===null?Xd(t,null,a,l):Dn(t,e.child,a,l)}function Id(e,t,a,l,u){a=a.render;var c=t.ref;if("ref"in l){var h={};for(var v in l)v!=="ref"&&(h[v]=l[v])}else h=l;return Xl(t),l=Wo(e,t,a,h,c,u),v=Po(),e!==null&&!yt?(Io(e,t,u),Ga(e,t,u)):(He&&v&&Uo(t),t.flags|=1,bt(e,t,l,u),t.child)}function eh(e,t,a,l,u){if(e===null){var c=a.type;return typeof c=="function"&&!_o(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,th(e,t,c,l,u)):(e=zu(a.type,null,l,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!xc(e,u)){var h=c.memoizedProps;if(a=a.compare,a=a!==null?a:ii,a(h,l)&&e.ref===t.ref)return Ga(e,t,u)}return t.flags|=1,e=Ua(c,l),e.ref=t.ref,e.return=t,t.child=e}function th(e,t,a,l,u){if(e!==null){var c=e.memoizedProps;if(ii(c,l)&&e.ref===t.ref)if(yt=!1,t.pendingProps=l=c,xc(e,u))(e.flags&131072)!==0&&(yt=!0);else return t.lanes=e.lanes,Ga(e,t,u)}return yc(e,t,a,l,u)}function ah(e,t,a){var l=t.pendingProps,u=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|a:a,e!==null){for(u=t.child=e.child,c=0;u!==null;)c=c|u.lanes|u.childLanes,u=u.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return lh(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Hu(t,c!==null?c.cachePool:null),c!==null?td(t,c):Jo(),Vd(t);else return t.lanes=t.childLanes=536870912,lh(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?(Hu(t,c.cachePool),td(t,c),il(),t.memoizedState=null):(e!==null&&Hu(t,null),Jo(),il());return bt(e,t,u,a),t.child}function lh(e,t,a,l){var u=Xo();return u=u===null?null:{parent:ht._currentValue,pool:u},t.memoizedState={baseLanes:a,cachePool:u},e!==null&&Hu(t,null),Jo(),Vd(t),e!==null&&si(e,t,l,!0),null}function er(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(o(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function yc(e,t,a,l,u){return Xl(t),a=Wo(e,t,a,l,void 0,u),l=Po(),e!==null&&!yt?(Io(e,t,u),Ga(e,t,u)):(He&&l&&Uo(t),t.flags|=1,bt(e,t,a,u),t.child)}function nh(e,t,a,l,u,c){return Xl(t),t.updateQueue=null,a=ld(t,l,a,u),ad(e),l=Po(),e!==null&&!yt?(Io(e,t,c),Ga(e,t,c)):(He&&l&&Uo(t),t.flags|=1,bt(e,t,a,c),t.child)}function ih(e,t,a,l,u){if(Xl(t),t.stateNode===null){var c=gn,h=a.contextType;typeof h=="object"&&h!==null&&(c=xt(h)),c=new a(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=hc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},Qo(t),h=a.contextType,c.context=typeof h=="object"&&h!==null?xt(h):gn,c.state=t.memoizedState,h=a.getDerivedStateFromProps,typeof h=="function"&&(dc(t,a,h,l),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(h=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),h!==c.state&&hc.enqueueReplaceState(c,c.state,null),vi(t,l,c,u),pi(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var v=t.memoizedProps,S=Zl(a,v);c.props=S;var N=c.context,Y=a.contextType;h=gn,typeof Y=="object"&&Y!==null&&(h=xt(Y));var V=a.getDerivedStateFromProps;Y=typeof V=="function"||typeof c.getSnapshotBeforeUpdate=="function",v=t.pendingProps!==v,Y||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(v||N!==h)&&Zd(t,c,l,h),el=!1;var z=t.memoizedState;c.state=z,vi(t,l,c,u),pi(),N=t.memoizedState,v||z!==N||el?(typeof V=="function"&&(dc(t,a,V,l),N=t.memoizedState),(S=el||Qd(t,a,S,l,z,N,h))?(Y||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=N),c.props=l,c.state=N,c.context=h,l=S):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Zo(e,t),h=t.memoizedProps,Y=Zl(a,h),c.props=Y,V=t.pendingProps,z=c.context,N=a.contextType,S=gn,typeof N=="object"&&N!==null&&(S=xt(N)),v=a.getDerivedStateFromProps,(N=typeof v=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h!==V||z!==S)&&Zd(t,c,l,S),el=!1,z=t.memoizedState,c.state=z,vi(t,l,c,u),pi();var L=t.memoizedState;h!==V||z!==L||el||e!==null&&e.dependencies!==null&&Lu(e.dependencies)?(typeof v=="function"&&(dc(t,a,v,l),L=t.memoizedState),(Y=el||Qd(t,a,Y,l,z,L,S)||e!==null&&e.dependencies!==null&&Lu(e.dependencies))?(N||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,L,S),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,L,S)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=L),c.props=l,c.state=L,c.context=S,l=Y):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,er(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=Dn(t,e.child,null,u),t.child=Dn(t,null,a,u)):bt(e,t,a,u),t.memoizedState=c.state,e=t.child):e=Ga(e,t,u),e}function uh(e,t,a,l){return oi(),t.flags|=256,bt(e,t,a,l),t.child}var pc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function vc(e){return{baseLanes:e,cachePool:kf()}}function gc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=ta),e}function rh(e,t,a){var l=t.pendingProps,u=!1,c=(t.flags&128)!==0,h;if((h=c)||(h=e!==null&&e.memoizedState===null?!1:(mt.current&2)!==0),h&&(u=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(He){if(u?nl(t):il(),He){var v=lt,S;if(S=v){e:{for(S=v,v=va;S.nodeType!==8;){if(!v){v=null;break e}if(S=sa(S.nextSibling),S===null){v=null;break e}}v=S}v!==null?(t.memoizedState={dehydrated:v,treeContext:Hl!==null?{id:La,overflow:ja}:null,retryLane:536870912,hydrationErrors:null},S=Yt(18,null,null,0),S.stateNode=v,S.return=t,t.child=S,wt=t,lt=null,S=!0):S=!1}S||Yl(t)}if(v=t.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return ts(v)?t.lanes=32:t.lanes=536870912,null;Ya(t)}return v=l.children,l=l.fallback,u?(il(),u=t.mode,v=tr({mode:"hidden",children:v},u),l=jl(l,u,a,null),v.return=t,l.return=t,v.sibling=l,t.child=v,u=t.child,u.memoizedState=vc(a),u.childLanes=gc(e,h,a),t.memoizedState=pc,l):(nl(t),bc(t,v))}if(S=e.memoizedState,S!==null&&(v=S.dehydrated,v!==null)){if(c)t.flags&256?(nl(t),t.flags&=-257,t=Sc(e,t,a)):t.memoizedState!==null?(il(),t.child=e.child,t.flags|=128,t=null):(il(),u=l.fallback,v=t.mode,l=tr({mode:"visible",children:l.children},v),u=jl(u,v,a,null),u.flags|=2,l.return=t,u.return=t,l.sibling=u,t.child=l,Dn(t,e.child,null,a),l=t.child,l.memoizedState=vc(a),l.childLanes=gc(e,h,a),t.memoizedState=pc,t=u);else if(nl(t),ts(v)){if(h=v.nextSibling&&v.nextSibling.dataset,h)var N=h.dgst;h=N,l=Error(o(419)),l.stack="",l.digest=h,ci({value:l,source:null,stack:null}),t=Sc(e,t,a)}else if(yt||si(e,t,a,!1),h=(a&e.childLanes)!==0,yt||h){if(h=Je,h!==null&&(l=a&-a,l=(l&42)!==0?1:$n(l),l=(l&(h.suspendedLanes|a))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,vn(e,l),Zt(h,e,l),Pd;v.data==="$?"||Bc(),t=Sc(e,t,a)}else v.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,lt=sa(v.nextSibling),wt=t,He=!0,ql=null,va=!1,e!==null&&(Pt[It++]=La,Pt[It++]=ja,Pt[It++]=Hl,La=e.id,ja=e.overflow,Hl=t),t=bc(t,l.children),t.flags|=4096);return t}return u?(il(),u=l.fallback,v=t.mode,S=e.child,N=S.sibling,l=Ua(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,N!==null?u=Ua(N,u):(u=jl(u,v,a,null),u.flags|=2),u.return=t,l.return=t,l.sibling=u,t.child=l,l=u,u=t.child,v=e.child.memoizedState,v===null?v=vc(a):(S=v.cachePool,S!==null?(N=ht._currentValue,S=S.parent!==N?{parent:N,pool:N}:S):S=kf(),v={baseLanes:v.baseLanes|a,cachePool:S}),u.memoizedState=v,u.childLanes=gc(e,h,a),t.memoizedState=pc,l):(nl(t),a=e.child,e=a.sibling,a=Ua(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=a,t.memoizedState=null,a)}function bc(e,t){return t=tr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function tr(e,t){return e=Yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Sc(e,t,a){return Dn(t,e.child,null,a),e=bc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function oh(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Bo(e.return,t,a)}function Ec(e,t,a,l,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=a,c.tailMode=u)}function ch(e,t,a){var l=t.pendingProps,u=l.revealOrder,c=l.tail;if(bt(e,t,l.children,a),l=mt.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&oh(e,a,t);else if(e.tag===19)oh(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(P(mt,l),u){case"forwards":for(a=t.child,u=null;a!==null;)e=a.alternate,e!==null&&Wu(e)===null&&(u=a),a=a.sibling;a=u,a===null?(u=t.child,t.child=null):(u=a.sibling,a.sibling=null),Ec(t,!1,u,a,c);break;case"backwards":for(a=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Wu(e)===null){t.child=u;break}e=u.sibling,u.sibling=a,a=u,u=e}Ec(t,!0,a,null,c);break;case"together":Ec(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ga(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),sl|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(si(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,a=Ua(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Ua(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function xc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Lu(e)))}function Yp(e,t,a){switch(t.tag){case 3:ke(t,t.stateNode.containerInfo),Ia(t,ht,e.memoizedState.cache),oi();break;case 27:case 5:ot(t);break;case 4:ke(t,t.stateNode.containerInfo);break;case 10:Ia(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(nl(t),t.flags|=128,null):(a&t.child.childLanes)!==0?rh(e,t,a):(nl(t),e=Ga(e,t,a),e!==null?e.sibling:null);nl(t);break;case 19:var u=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(si(e,t,a,!1),l=(a&t.childLanes)!==0),u){if(l)return ch(e,t,a);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),P(mt,mt.current),l)break;return null;case 22:case 23:return t.lanes=0,ah(e,t,a);case 24:Ia(t,ht,e.memoizedState.cache)}return Ga(e,t,a)}function sh(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)yt=!0;else{if(!xc(e,a)&&(t.flags&128)===0)return yt=!1,Yp(e,t,a);yt=(e.flags&131072)!==0}else yt=!1,He&&(t.flags&1048576)!==0&&Yf(t,Uu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,u=l._init;if(l=u(l._payload),t.type=l,typeof l=="function")_o(l)?(e=Zl(l,e),t.tag=1,t=ih(null,t,l,e,a)):(t.tag=0,t=yc(null,t,l,e,a));else{if(l!=null){if(u=l.$$typeof,u===ye){t.tag=11,t=Id(null,t,l,e,a);break e}else if(u===ce){t.tag=14,t=eh(null,t,l,e,a);break e}}throw t=Le(l)||l,Error(o(306,t,""))}}return t;case 0:return yc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,u=Zl(l,t.pendingProps),ih(e,t,l,u,a);case 3:e:{if(ke(t,t.stateNode.containerInfo),e===null)throw Error(o(387));l=t.pendingProps;var c=t.memoizedState;u=c.element,Zo(e,t),vi(t,l,null,a);var h=t.memoizedState;if(l=h.cache,Ia(t,ht,l),l!==c.cache&&qo(t,[ht],a,!0),pi(),l=h.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=uh(e,t,l,a);break e}else if(l!==u){u=$t(Error(o(424)),t),ci(u),t=uh(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(lt=sa(e.firstChild),wt=t,He=!0,ql=null,va=!0,a=Xd(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(oi(),l===u){t=Ga(e,t,a);break e}bt(e,t,l,a)}t=t.child}return t;case 26:return er(e,t),e===null?(a=mm(t.type,null,t.pendingProps,null))?t.memoizedState=a:He||(a=t.type,e=t.pendingProps,l=yr(he.current).createElement(a),l[Z]=t,l[k]=e,Et(l,a,e),be(l),t.stateNode=l):t.memoizedState=mm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ot(t),e===null&&He&&(l=t.stateNode=fm(t.type,t.pendingProps,he.current),wt=t,va=!0,u=lt,ml(t.type)?(as=u,lt=sa(l.firstChild)):lt=u),bt(e,t,t.pendingProps.children,a),er(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&He&&((u=l=lt)&&(l=mv(l,t.type,t.pendingProps,va),l!==null?(t.stateNode=l,wt=t,lt=sa(l.firstChild),va=!1,u=!0):u=!1),u||Yl(t)),ot(t),u=t.type,c=t.pendingProps,h=e!==null?e.memoizedProps:null,l=c.children,Pc(u,c)?l=null:h!==null&&Pc(u,h)&&(t.flags|=32),t.memoizedState!==null&&(u=Wo(e,t,zp,null,null,a),qi._currentValue=u),er(e,t),bt(e,t,l,a),t.child;case 6:return e===null&&He&&((e=a=lt)&&(a=yv(a,t.pendingProps,va),a!==null?(t.stateNode=a,wt=t,lt=null,e=!0):e=!1),e||Yl(t)),null;case 13:return rh(e,t,a);case 4:return ke(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Dn(t,null,l,a):bt(e,t,l,a),t.child;case 11:return Id(e,t,t.type,t.pendingProps,a);case 7:return bt(e,t,t.pendingProps,a),t.child;case 8:return bt(e,t,t.pendingProps.children,a),t.child;case 12:return bt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Ia(t,t.type,l.value),bt(e,t,l.children,a),t.child;case 9:return u=t.type._context,l=t.pendingProps.children,Xl(t),u=xt(u),l=l(u),t.flags|=1,bt(e,t,l,a),t.child;case 14:return eh(e,t,t.type,t.pendingProps,a);case 15:return th(e,t,t.type,t.pendingProps,a);case 19:return ch(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=tr(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Ua(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ah(e,t,a);case 24:return Xl(t),l=xt(ht),e===null?(u=Xo(),u===null&&(u=Je,c=Yo(),u.pooledCache=c,c.refCount++,c!==null&&(u.pooledCacheLanes|=a),u=c),t.memoizedState={parent:l,cache:u},Qo(t),Ia(t,ht,u)):((e.lanes&a)!==0&&(Zo(e,t),vi(t,null,null,a),pi()),u=e.memoizedState,c=t.memoizedState,u.parent!==l?(u={parent:l,cache:l},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Ia(t,ht,l)):(l=c.cache,Ia(t,ht,l),l!==u.cache&&qo(t,[ht],a,!0))),bt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Xa(e){e.flags|=4}function fh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!bm(t)){if(t=ea.current,t!==null&&((_e&4194048)===_e?ga!==null:(_e&62914560)!==_e&&(_e&536870912)===0||t!==ga))throw mi=Vo,Jf;e.flags|=8192}}function ar(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?pu():536870912,e.lanes|=t,zn|=t)}function Ti(e,t){if(!He)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function tt(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var u=e.child;u!==null;)a|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)a|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Gp(e,t,a){var l=t.pendingProps;switch(Lo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return tt(t),null;case 1:return tt(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Ba(ht),ha(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(ri(t)?Xa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Vf())),tt(t),null;case 26:return a=t.memoizedState,e===null?(Xa(t),a!==null?(tt(t),fh(t,a)):(tt(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Xa(t),tt(t),fh(t,a)):(tt(t),t.flags&=-16777217):(e.memoizedProps!==l&&Xa(t),tt(t),t.flags&=-16777217),null;case 27:ia(t),a=he.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Xa(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return tt(t),null}e=ue.current,ri(t)?Gf(t):(e=fm(u,l,a),t.stateNode=e,Xa(t))}return tt(t),null;case 5:if(ia(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Xa(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return tt(t),null}if(e=ue.current,ri(t))Gf(t);else{switch(u=yr(he.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?u.createElement(a,{is:l.is}):u.createElement(a)}}e[Z]=t,e[k]=l;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Et(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xa(t)}}return tt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Xa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(e=he.current,ri(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,u=wt,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}e[Z]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||nm(e.nodeValue,a)),e||Yl(t)}else e=yr(e).createTextNode(l),e[Z]=t,t.stateNode=e}return tt(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=ri(t),l!==null&&l.dehydrated!==null){if(e===null){if(!u)throw Error(o(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(o(317));u[Z]=t}else oi(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;tt(t),u=!1}else u=Vf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Ya(t),t):(Ya(t),null)}if(Ya(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==u&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),ar(t,t.updateQueue),tt(t),null;case 4:return ha(),e===null&&kc(t.stateNode.containerInfo),tt(t),null;case 10:return Ba(t.type),tt(t),null;case 19:if(I(mt),u=t.memoizedState,u===null)return tt(t),null;if(l=(t.flags&128)!==0,c=u.rendering,c===null)if(l)Ti(u,!1);else{if(nt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Wu(e),c!==null){for(t.flags|=128,Ti(u,!1),e=c.updateQueue,t.updateQueue=e,ar(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)qf(a,e),a=a.sibling;return P(mt,mt.current&1|2),t.child}e=e.sibling}u.tail!==null&&Ht()>ir&&(t.flags|=128,l=!0,Ti(u,!1),t.lanes=4194304)}else{if(!l)if(e=Wu(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,ar(t,e),Ti(u,!0),u.tail===null&&u.tailMode==="hidden"&&!c.alternate&&!He)return tt(t),null}else 2*Ht()-u.renderingStartTime>ir&&a!==536870912&&(t.flags|=128,l=!0,Ti(u,!1),t.lanes=4194304);u.isBackwards?(c.sibling=t.child,t.child=c):(e=u.last,e!==null?e.sibling=c:t.child=c,u.last=c)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Ht(),t.sibling=null,e=mt.current,P(mt,l?e&1|2:e&1),t):(tt(t),null);case 22:case 23:return Ya(t),Fo(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(tt(t),t.subtreeFlags&6&&(t.flags|=8192)):tt(t),a=t.updateQueue,a!==null&&ar(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&I(Vl),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Ba(ht),tt(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function Xp(e,t){switch(Lo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ba(ht),ha(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ia(t),null;case 13:if(Ya(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));oi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(mt),null;case 4:return ha(),null;case 10:return Ba(t.type),null;case 22:case 23:return Ya(t),Fo(),e!==null&&I(Vl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ba(ht),null;case 25:return null;default:return null}}function dh(e,t){switch(Lo(t),t.tag){case 3:Ba(ht),ha();break;case 26:case 27:case 5:ia(t);break;case 4:ha();break;case 13:Ya(t);break;case 19:I(mt);break;case 10:Ba(t.type);break;case 22:case 23:Ya(t),Fo(),e!==null&&I(Vl);break;case 24:Ba(ht)}}function Oi(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var u=l.next;a=u;do{if((a.tag&e)===e){l=void 0;var c=a.create,h=a.inst;l=c(),h.destroy=l}a=a.next}while(a!==u)}}catch(v){Ke(t,t.return,v)}}function ul(e,t,a){try{var l=t.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var c=u.next;l=c;do{if((l.tag&e)===e){var h=l.inst,v=h.destroy;if(v!==void 0){h.destroy=void 0,u=t;var S=a,N=v;try{N()}catch(Y){Ke(u,S,Y)}}}l=l.next}while(l!==c)}}catch(Y){Ke(t,t.return,Y)}}function hh(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{ed(t,a)}catch(l){Ke(e,e.return,l)}}}function mh(e,t,a){a.props=Zl(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Ke(e,t,l)}}function Ai(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(u){Ke(e,t,u)}}function ba(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(u){Ke(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(u){Ke(e,t,u)}else a.current=null}function yh(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(u){Ke(e,e.return,u)}}function Rc(e,t,a){try{var l=e.stateNode;cv(l,e.type,a,t),l[k]=t}catch(u){Ke(e,e.return,u)}}function ph(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ml(e.type)||e.tag===4}function Tc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ph(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ml(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Oc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=mr));else if(l!==4&&(l===27&&ml(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Oc(e,t,a),e=e.sibling;e!==null;)Oc(e,t,a),e=e.sibling}function lr(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&ml(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(lr(e,t,a),e=e.sibling;e!==null;)lr(e,t,a),e=e.sibling}function vh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Et(t,l,a),t[Z]=e,t[k]=a}catch(c){Ke(e,e.return,c)}}var Va=!1,ut=!1,Ac=!1,gh=typeof WeakSet=="function"?WeakSet:Set,pt=null;function Vp(e,t){if(e=e.containerInfo,$c=Er,e=Mf(e),To(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var u=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var h=0,v=-1,S=-1,N=0,Y=0,V=e,z=null;t:for(;;){for(var L;V!==a||u!==0&&V.nodeType!==3||(v=h+u),V!==c||l!==0&&V.nodeType!==3||(S=h+l),V.nodeType===3&&(h+=V.nodeValue.length),(L=V.firstChild)!==null;)z=V,V=L;for(;;){if(V===e)break t;if(z===a&&++N===u&&(v=h),z===c&&++Y===l&&(S=h),(L=V.nextSibling)!==null)break;V=z,z=V.parentNode}V=L}a=v===-1||S===-1?null:{start:v,end:S}}else a=null}a=a||{start:0,end:0}}else a=null;for(Wc={focusedElem:e,selectionRange:a},Er=!1,pt=t;pt!==null;)if(t=pt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,pt=e;else for(;pt!==null;){switch(t=pt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,u=c.memoizedProps,c=c.memoizedState,l=a.stateNode;try{var ve=Zl(a.type,u,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(ve,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(de){Ke(a,a.return,de)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)es(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":es(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,pt=e;break}pt=t.return}}function bh(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:rl(e,a),l&4&&Oi(5,a);break;case 1:if(rl(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(h){Ke(a,a.return,h)}else{var u=Zl(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Ke(a,a.return,h)}}l&64&&hh(a),l&512&&Ai(a,a.return);break;case 3:if(rl(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{ed(e,t)}catch(h){Ke(a,a.return,h)}}break;case 27:t===null&&l&4&&vh(a);case 26:case 5:rl(e,a),t===null&&l&4&&yh(a),l&512&&Ai(a,a.return);break;case 12:rl(e,a);break;case 13:rl(e,a),l&4&&xh(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Pp.bind(null,a),pv(e,a))));break;case 22:if(l=a.memoizedState!==null||Va,!l){t=t!==null&&t.memoizedState!==null||ut,u=Va;var c=ut;Va=l,(ut=t)&&!c?ol(e,a,(a.subtreeFlags&8772)!==0):rl(e,a),Va=u,ut=c}break;case 30:break;default:rl(e,a)}}function Sh(e){var t=e.alternate;t!==null&&(e.alternate=null,Sh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ie(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var We=null,zt=!1;function Qa(e,t,a){for(a=a.child;a!==null;)Eh(e,t,a),a=a.sibling}function Eh(e,t,a){if(Ie&&typeof Ie.onCommitFiberUnmount=="function")try{Ie.onCommitFiberUnmount(Dt,a)}catch{}switch(a.tag){case 26:ut||ba(a,t),Qa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:ut||ba(a,t);var l=We,u=zt;ml(a.type)&&(We=a.stateNode,zt=!1),Qa(e,t,a),Li(a.stateNode),We=l,zt=u;break;case 5:ut||ba(a,t);case 6:if(l=We,u=zt,We=null,Qa(e,t,a),We=l,zt=u,We!==null)if(zt)try{(We.nodeType===9?We.body:We.nodeName==="HTML"?We.ownerDocument.body:We).removeChild(a.stateNode)}catch(c){Ke(a,t,c)}else try{We.removeChild(a.stateNode)}catch(c){Ke(a,t,c)}break;case 18:We!==null&&(zt?(e=We,cm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Vi(e)):cm(We,a.stateNode));break;case 4:l=We,u=zt,We=a.stateNode.containerInfo,zt=!0,Qa(e,t,a),We=l,zt=u;break;case 0:case 11:case 14:case 15:ut||ul(2,a,t),ut||ul(4,a,t),Qa(e,t,a);break;case 1:ut||(ba(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&mh(a,t,l)),Qa(e,t,a);break;case 21:Qa(e,t,a);break;case 22:ut=(l=ut)||a.memoizedState!==null,Qa(e,t,a),ut=l;break;default:Qa(e,t,a)}}function xh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Vi(e)}catch(a){Ke(t,t.return,a)}}function Qp(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new gh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new gh),t;default:throw Error(o(435,e.tag))}}function wc(e,t){var a=Qp(e);t.forEach(function(l){var u=Ip.bind(null,e,l);a.has(l)||(a.add(l),l.then(u,u))})}function Gt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var u=a[l],c=e,h=t,v=h;e:for(;v!==null;){switch(v.tag){case 27:if(ml(v.type)){We=v.stateNode,zt=!1;break e}break;case 5:We=v.stateNode,zt=!1;break e;case 3:case 4:We=v.stateNode.containerInfo,zt=!0;break e}v=v.return}if(We===null)throw Error(o(160));Eh(c,h,u),We=null,zt=!1,c=u.alternate,c!==null&&(c.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Rh(t,e),t=t.sibling}var ca=null;function Rh(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Gt(t,e),Xt(e),l&4&&(ul(3,e,e.return),Oi(3,e),ul(5,e,e.return));break;case 1:Gt(t,e),Xt(e),l&512&&(ut||a===null||ba(a,a.return)),l&64&&Va&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var u=ca;if(Gt(t,e),Xt(e),l&512&&(ut||a===null||ba(a,a.return)),l&4){var c=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,u=u.ownerDocument||u;t:switch(l){case"title":c=u.getElementsByTagName("title")[0],(!c||c[ne]||c[Z]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=u.createElement(l),u.head.insertBefore(c,u.querySelector("head > title"))),Et(c,l,a),c[Z]=e,be(c),l=c;break e;case"link":var h=vm("link","href",u).get(l+(a.href||""));if(h){for(var v=0;v<h.length;v++)if(c=h[v],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){h.splice(v,1);break t}}c=u.createElement(l),Et(c,l,a),u.head.appendChild(c);break;case"meta":if(h=vm("meta","content",u).get(l+(a.content||""))){for(v=0;v<h.length;v++)if(c=h[v],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){h.splice(v,1);break t}}c=u.createElement(l),Et(c,l,a),u.head.appendChild(c);break;default:throw Error(o(468,l))}c[Z]=e,be(c),l=c}e.stateNode=l}else gm(u,e.type,e.stateNode);else e.stateNode=pm(u,l,e.memoizedProps);else c!==l?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,l===null?gm(u,e.type,e.stateNode):pm(u,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Rc(e,e.memoizedProps,a.memoizedProps)}break;case 27:Gt(t,e),Xt(e),l&512&&(ut||a===null||ba(a,a.return)),a!==null&&l&4&&Rc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Gt(t,e),Xt(e),l&512&&(ut||a===null||ba(a,a.return)),e.flags&32){u=e.stateNode;try{sn(u,"")}catch(L){Ke(e,e.return,L)}}l&4&&e.stateNode!=null&&(u=e.memoizedProps,Rc(e,u,a!==null?a.memoizedProps:u)),l&1024&&(Ac=!0);break;case 6:if(Gt(t,e),Xt(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(L){Ke(e,e.return,L)}}break;case 3:if(gr=null,u=ca,ca=pr(t.containerInfo),Gt(t,e),ca=u,Xt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Vi(t.containerInfo)}catch(L){Ke(e,e.return,L)}Ac&&(Ac=!1,Th(e));break;case 4:l=ca,ca=pr(e.stateNode.containerInfo),Gt(t,e),Xt(e),ca=l;break;case 12:Gt(t,e),Xt(e);break;case 13:Gt(t,e),Xt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Cc=Ht()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,wc(e,l)));break;case 22:u=e.memoizedState!==null;var S=a!==null&&a.memoizedState!==null,N=Va,Y=ut;if(Va=N||u,ut=Y||S,Gt(t,e),ut=Y,Va=N,Xt(e),l&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(a===null||S||Va||ut||Kl(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){S=a=t;try{if(c=S.stateNode,u)h=c.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{v=S.stateNode;var V=S.memoizedProps.style,z=V!=null&&V.hasOwnProperty("display")?V.display:null;v.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch(L){Ke(S,S.return,L)}}}else if(t.tag===6){if(a===null){S=t;try{S.stateNode.nodeValue=u?"":S.memoizedProps}catch(L){Ke(S,S.return,L)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,wc(e,a))));break;case 19:Gt(t,e),Xt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,wc(e,l)));break;case 30:break;case 21:break;default:Gt(t,e),Xt(e)}}function Xt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(ph(l)){a=l;break}l=l.return}if(a==null)throw Error(o(160));switch(a.tag){case 27:var u=a.stateNode,c=Tc(e);lr(e,c,u);break;case 5:var h=a.stateNode;a.flags&32&&(sn(h,""),a.flags&=-33);var v=Tc(e);lr(e,v,h);break;case 3:case 4:var S=a.stateNode.containerInfo,N=Tc(e);Oc(e,N,S);break;default:throw Error(o(161))}}catch(Y){Ke(e,e.return,Y)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Th(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Th(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function rl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)bh(e,t.alternate,t),t=t.sibling}function Kl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ul(4,t,t.return),Kl(t);break;case 1:ba(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&mh(t,t.return,a),Kl(t);break;case 27:Li(t.stateNode);case 26:case 5:ba(t,t.return),Kl(t);break;case 22:t.memoizedState===null&&Kl(t);break;case 30:Kl(t);break;default:Kl(t)}e=e.sibling}}function ol(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,u=e,c=t,h=c.flags;switch(c.tag){case 0:case 11:case 15:ol(u,c,a),Oi(4,c);break;case 1:if(ol(u,c,a),l=c,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(N){Ke(l,l.return,N)}if(l=c,u=l.updateQueue,u!==null){var v=l.stateNode;try{var S=u.shared.hiddenCallbacks;if(S!==null)for(u.shared.hiddenCallbacks=null,u=0;u<S.length;u++)If(S[u],v)}catch(N){Ke(l,l.return,N)}}a&&h&64&&hh(c),Ai(c,c.return);break;case 27:vh(c);case 26:case 5:ol(u,c,a),a&&l===null&&h&4&&yh(c),Ai(c,c.return);break;case 12:ol(u,c,a);break;case 13:ol(u,c,a),a&&h&4&&xh(u,c);break;case 22:c.memoizedState===null&&ol(u,c,a),Ai(c,c.return);break;case 30:break;default:ol(u,c,a)}t=t.sibling}}function Dc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&fi(a))}function Mc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&fi(e))}function Sa(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Oh(e,t,a,l),t=t.sibling}function Oh(e,t,a,l){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Sa(e,t,a,l),u&2048&&Oi(9,t);break;case 1:Sa(e,t,a,l);break;case 3:Sa(e,t,a,l),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&fi(e)));break;case 12:if(u&2048){Sa(e,t,a,l),e=t.stateNode;try{var c=t.memoizedProps,h=c.id,v=c.onPostCommit;typeof v=="function"&&v(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){Ke(t,t.return,S)}}else Sa(e,t,a,l);break;case 13:Sa(e,t,a,l);break;case 23:break;case 22:c=t.stateNode,h=t.alternate,t.memoizedState!==null?c._visibility&2?Sa(e,t,a,l):wi(e,t):c._visibility&2?Sa(e,t,a,l):(c._visibility|=2,Mn(e,t,a,l,(t.subtreeFlags&10256)!==0)),u&2048&&Dc(h,t);break;case 24:Sa(e,t,a,l),u&2048&&Mc(t.alternate,t);break;default:Sa(e,t,a,l)}}function Mn(e,t,a,l,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,h=t,v=a,S=l,N=h.flags;switch(h.tag){case 0:case 11:case 15:Mn(c,h,v,S,u),Oi(8,h);break;case 23:break;case 22:var Y=h.stateNode;h.memoizedState!==null?Y._visibility&2?Mn(c,h,v,S,u):wi(c,h):(Y._visibility|=2,Mn(c,h,v,S,u)),u&&N&2048&&Dc(h.alternate,h);break;case 24:Mn(c,h,v,S,u),u&&N&2048&&Mc(h.alternate,h);break;default:Mn(c,h,v,S,u)}t=t.sibling}}function wi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,u=l.flags;switch(l.tag){case 22:wi(a,l),u&2048&&Dc(l.alternate,l);break;case 24:wi(a,l),u&2048&&Mc(l.alternate,l);break;default:wi(a,l)}t=t.sibling}}var Di=8192;function Nn(e){if(e.subtreeFlags&Di)for(e=e.child;e!==null;)Ah(e),e=e.sibling}function Ah(e){switch(e.tag){case 26:Nn(e),e.flags&Di&&e.memoizedState!==null&&Mv(ca,e.memoizedState,e.memoizedProps);break;case 5:Nn(e);break;case 3:case 4:var t=ca;ca=pr(e.stateNode.containerInfo),Nn(e),ca=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Di,Di=16777216,Nn(e),Di=t):Nn(e));break;default:Nn(e)}}function wh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Mi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];pt=l,Mh(l,e)}wh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Dh(e),e=e.sibling}function Dh(e){switch(e.tag){case 0:case 11:case 15:Mi(e),e.flags&2048&&ul(9,e,e.return);break;case 3:Mi(e);break;case 12:Mi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,nr(e)):Mi(e);break;default:Mi(e)}}function nr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];pt=l,Mh(l,e)}wh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ul(8,t,t.return),nr(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,nr(t));break;default:nr(t)}e=e.sibling}}function Mh(e,t){for(;pt!==null;){var a=pt;switch(a.tag){case 0:case 11:case 15:ul(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:fi(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,pt=l;else e:for(a=e;pt!==null;){l=pt;var u=l.sibling,c=l.return;if(Sh(l),l===a){pt=null;break e}if(u!==null){u.return=c,pt=u;break e}pt=c}}}var Zp={getCacheForType:function(e){var t=xt(ht),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Kp=typeof WeakMap=="function"?WeakMap:Map,qe=0,Je=null,De=null,_e=0,Ye=0,Vt=null,cl=!1,_n=!1,Nc=!1,Za=0,nt=0,sl=0,kl=0,_c=0,ta=0,zn=0,Ni=null,Ct=null,zc=!1,Cc=0,ir=1/0,ur=null,fl=null,St=0,dl=null,Cn=null,Un=0,Uc=0,Lc=null,Nh=null,_i=0,jc=null;function Qt(){if((qe&2)!==0&&_e!==0)return _e&-_e;if(C.T!==null){var e=En;return e!==0?e:Vc()}return x()}function _h(){ta===0&&(ta=(_e&536870912)===0||He?un():536870912);var e=ea.current;return e!==null&&(e.flags|=32),ta}function Zt(e,t,a){(e===Je&&(Ye===2||Ye===9)||e.cancelPendingCommit!==null)&&(Ln(e,0),hl(e,_e,ta,!1)),Nl(e,a),((qe&2)===0||e!==Je)&&(e===Je&&((qe&2)===0&&(kl|=a),nt===4&&hl(e,_e,ta,!1)),Ea(e))}function zh(e,t,a){if((qe&6)!==0)throw Error(o(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ya(e,t),u=l?Fp(e,t):qc(e,t,!0),c=l;do{if(u===0){_n&&!l&&hl(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!kp(a)){u=qc(e,t,!1),c=!1;continue}if(u===2){if(c=t,e.errorRecoveryDisabledLanes&c)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var v=e;u=Ni;var S=v.current.memoizedState.isDehydrated;if(S&&(Ln(v,h).flags|=256),h=qc(v,h,!1),h!==2){if(Nc&&!S){v.errorRecoveryDisabledLanes|=c,kl|=c,u=4;break e}c=Ct,Ct=u,c!==null&&(Ct===null?Ct=c:Ct.push.apply(Ct,c))}u=h}if(c=!1,u!==2)continue}}if(u===1){Ln(e,0),hl(e,t,0,!0);break}e:{switch(l=e,c=u,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:hl(l,t,ta,!cl);break e;case 2:Ct=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(u=Cc+300-Ht(),10<u)){if(hl(l,t,ta,!cl),Ml(l,0,!0)!==0)break e;l.timeoutHandle=rm(Ch.bind(null,l,a,Ct,ur,zc,t,ta,kl,zn,cl,c,2,-0,0),u);break e}Ch(l,a,Ct,ur,zc,t,ta,kl,zn,cl,c,0,-0,0)}}break}while(!0);Ea(e)}function Ch(e,t,a,l,u,c,h,v,S,N,Y,V,z,L){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(Bi={stylesheets:null,count:0,unsuspend:Dv},Ah(t),V=Nv(),V!==null)){e.cancelPendingCommit=V(Yh.bind(null,e,t,c,a,l,u,h,v,S,Y,1,z,L)),hl(e,c,h,!N);return}Yh(e,t,c,a,l,u,h,v,S)}function kp(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var u=a[l],c=u.getSnapshot;u=u.value;try{if(!qt(c(),u))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function hl(e,t,a,l){t&=~_c,t&=~kl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var u=t;0<u;){var c=31-gt(u),h=1<<c;l[c]=-1,u&=~h}a!==0&&_l(e,a,t)}function rr(){return(qe&6)===0?(zi(0),!1):!0}function Hc(){if(De!==null){if(Ye===0)var e=De.return;else e=De,Ha=Gl=null,ec(e),wn=null,xi=0,e=De;for(;e!==null;)dh(e.alternate,e),e=e.return;De=null}}function Ln(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,fv(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Hc(),Je=e,De=a=Ua(e.current,null),_e=t,Ye=0,Vt=null,cl=!1,_n=ya(e,t),Nc=!1,zn=ta=_c=kl=sl=nt=0,Ct=Ni=null,zc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var u=31-gt(l),c=1<<u;t|=e[u],l&=~c}return Za=t,Mu(),a}function Uh(e,t){Oe=null,C.H=Ju,t===hi||t===Bu?(t=Wf(),Ye=3):t===Jf?(t=Wf(),Ye=4):Ye=t===Pd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Vt=t,De===null&&(nt=1,Iu(e,$t(t,e.current)))}function Lh(){var e=C.H;return C.H=Ju,e===null?Ju:e}function jh(){var e=C.A;return C.A=Zp,e}function Bc(){nt=4,cl||(_e&4194048)!==_e&&ea.current!==null||(_n=!0),(sl&134217727)===0&&(kl&134217727)===0||Je===null||hl(Je,_e,ta,!1)}function qc(e,t,a){var l=qe;qe|=2;var u=Lh(),c=jh();(Je!==e||_e!==t)&&(ur=null,Ln(e,t)),t=!1;var h=nt;e:do try{if(Ye!==0&&De!==null){var v=De,S=Vt;switch(Ye){case 8:Hc(),h=6;break e;case 3:case 2:case 9:case 6:ea.current===null&&(t=!0);var N=Ye;if(Ye=0,Vt=null,jn(e,v,S,N),a&&_n){h=0;break e}break;default:N=Ye,Ye=0,Vt=null,jn(e,v,S,N)}}Jp(),h=nt;break}catch(Y){Uh(e,Y)}while(!0);return t&&e.shellSuspendCounter++,Ha=Gl=null,qe=l,C.H=u,C.A=c,De===null&&(Je=null,_e=0,Mu()),h}function Jp(){for(;De!==null;)Hh(De)}function Fp(e,t){var a=qe;qe|=2;var l=Lh(),u=jh();Je!==e||_e!==t?(ur=null,ir=Ht()+500,Ln(e,t)):_n=ya(e,t);e:do try{if(Ye!==0&&De!==null){t=De;var c=Vt;t:switch(Ye){case 1:Ye=0,Vt=null,jn(e,t,c,1);break;case 2:case 9:if(Ff(c)){Ye=0,Vt=null,Bh(t);break}t=function(){Ye!==2&&Ye!==9||Je!==e||(Ye=7),Ea(e)},c.then(t,t);break e;case 3:Ye=7;break e;case 4:Ye=5;break e;case 7:Ff(c)?(Ye=0,Vt=null,Bh(t)):(Ye=0,Vt=null,jn(e,t,c,7));break;case 5:var h=null;switch(De.tag){case 26:h=De.memoizedState;case 5:case 27:var v=De;if(!h||bm(h)){Ye=0,Vt=null;var S=v.sibling;if(S!==null)De=S;else{var N=v.return;N!==null?(De=N,or(N)):De=null}break t}}Ye=0,Vt=null,jn(e,t,c,5);break;case 6:Ye=0,Vt=null,jn(e,t,c,6);break;case 8:Hc(),nt=6;break e;default:throw Error(o(462))}}$p();break}catch(Y){Uh(e,Y)}while(!0);return Ha=Gl=null,C.H=l,C.A=u,qe=a,De!==null?0:(Je=null,_e=0,Mu(),nt)}function $p(){for(;De!==null&&!no();)Hh(De)}function Hh(e){var t=sh(e.alternate,e,Za);e.memoizedProps=e.pendingProps,t===null?or(e):De=t}function Bh(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=nh(a,t,t.pendingProps,t.type,void 0,_e);break;case 11:t=nh(a,t,t.pendingProps,t.type.render,t.ref,_e);break;case 5:ec(t);default:dh(a,t),t=De=qf(t,Za),t=sh(a,t,Za)}e.memoizedProps=e.pendingProps,t===null?or(e):De=t}function jn(e,t,a,l){Ha=Gl=null,ec(t),wn=null,xi=0;var u=t.return;try{if(qp(e,u,t,a,_e)){nt=1,Iu(e,$t(a,e.current)),De=null;return}}catch(c){if(u!==null)throw De=u,c;nt=1,Iu(e,$t(a,e.current)),De=null;return}t.flags&32768?(He||l===1?e=!0:_n||(_e&536870912)!==0?e=!1:(cl=e=!0,(l===2||l===9||l===3||l===6)&&(l=ea.current,l!==null&&l.tag===13&&(l.flags|=16384))),qh(t,e)):or(t)}function or(e){var t=e;do{if((t.flags&32768)!==0){qh(t,cl);return}e=t.return;var a=Gp(t.alternate,t,Za);if(a!==null){De=a;return}if(t=t.sibling,t!==null){De=t;return}De=t=e}while(t!==null);nt===0&&(nt=5)}function qh(e,t){do{var a=Xp(e.alternate,e);if(a!==null){a.flags&=32767,De=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){De=e;return}De=e=a}while(e!==null);nt=6,De=null}function Yh(e,t,a,l,u,c,h,v,S){e.cancelPendingCommit=null;do cr();while(St!==0);if((qe&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=Mo,vu(e,a,c,h,v,S),e===Je&&(De=Je=null,_e=0),Cn=t,dl=e,Un=a,Uc=c,Lc=u,Nh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ev(nn,function(){return Zh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=C.T,C.T=null,u=W.p,W.p=2,h=qe,qe|=4;try{Vp(e,t,a)}finally{qe=h,W.p=u,C.T=l}}St=1,Gh(),Xh(),Vh()}}function Gh(){if(St===1){St=0;var e=dl,t=Cn,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=C.T,C.T=null;var l=W.p;W.p=2;var u=qe;qe|=4;try{Rh(t,e);var c=Wc,h=Mf(e.containerInfo),v=c.focusedElem,S=c.selectionRange;if(h!==v&&v&&v.ownerDocument&&Df(v.ownerDocument.documentElement,v)){if(S!==null&&To(v)){var N=S.start,Y=S.end;if(Y===void 0&&(Y=N),"selectionStart"in v)v.selectionStart=N,v.selectionEnd=Math.min(Y,v.value.length);else{var V=v.ownerDocument||document,z=V&&V.defaultView||window;if(z.getSelection){var L=z.getSelection(),ve=v.textContent.length,de=Math.min(S.start,ve),Qe=S.end===void 0?de:Math.min(S.end,ve);!L.extend&&de>Qe&&(h=Qe,Qe=de,de=h);var A=wf(v,de),R=wf(v,Qe);if(A&&R&&(L.rangeCount!==1||L.anchorNode!==A.node||L.anchorOffset!==A.offset||L.focusNode!==R.node||L.focusOffset!==R.offset)){var D=V.createRange();D.setStart(A.node,A.offset),L.removeAllRanges(),de>Qe?(L.addRange(D),L.extend(R.node,R.offset)):(D.setEnd(R.node,R.offset),L.addRange(D))}}}}for(V=[],L=v;L=L.parentNode;)L.nodeType===1&&V.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<V.length;v++){var G=V[v];G.element.scrollLeft=G.left,G.element.scrollTop=G.top}}Er=!!$c,Wc=$c=null}finally{qe=u,W.p=l,C.T=a}}e.current=t,St=2}}function Xh(){if(St===2){St=0;var e=dl,t=Cn,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=C.T,C.T=null;var l=W.p;W.p=2;var u=qe;qe|=4;try{bh(e,t.alternate,t)}finally{qe=u,W.p=l,C.T=a}}St=3}}function Vh(){if(St===4||St===3){St=0,io();var e=dl,t=Cn,a=Un,l=Nh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?St=5:(St=0,Cn=dl=null,Qh(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(fl=null),Wn(a),t=t.stateNode,Ie&&typeof Ie.onCommitFiberRoot=="function")try{Ie.onCommitFiberRoot(Dt,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=C.T,u=W.p,W.p=2,C.T=null;try{for(var c=e.onRecoverableError,h=0;h<l.length;h++){var v=l[h];c(v.value,{componentStack:v.stack})}}finally{C.T=t,W.p=u}}(Un&3)!==0&&cr(),Ea(e),u=e.pendingLanes,(a&4194090)!==0&&(u&42)!==0?e===jc?_i++:(_i=0,jc=e):_i=0,zi(0)}}function Qh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,fi(t)))}function cr(e){return Gh(),Xh(),Vh(),Zh()}function Zh(){if(St!==5)return!1;var e=dl,t=Uc;Uc=0;var a=Wn(Un),l=C.T,u=W.p;try{W.p=32>a?32:a,C.T=null,a=Lc,Lc=null;var c=dl,h=Un;if(St=0,Cn=dl=null,Un=0,(qe&6)!==0)throw Error(o(331));var v=qe;if(qe|=4,Dh(c.current),Oh(c,c.current,h,a),qe=v,zi(0,!1),Ie&&typeof Ie.onPostCommitFiberRoot=="function")try{Ie.onPostCommitFiberRoot(Dt,c)}catch{}return!0}finally{W.p=u,C.T=l,Qh(e,t)}}function Kh(e,t,a){t=$t(a,t),t=mc(e.stateNode,t,2),e=al(e,t,2),e!==null&&(Nl(e,2),Ea(e))}function Ke(e,t,a){if(e.tag===3)Kh(e,e,a);else for(;t!==null;){if(t.tag===3){Kh(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(fl===null||!fl.has(l))){e=$t(a,e),a=$d(2),l=al(t,a,2),l!==null&&(Wd(a,l,t,e),Nl(l,2),Ea(l));break}}t=t.return}}function Yc(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Kp;var u=new Set;l.set(t,u)}else u=l.get(t),u===void 0&&(u=new Set,l.set(t,u));u.has(a)||(Nc=!0,u.add(a),e=Wp.bind(null,e,t,a),t.then(e,e))}function Wp(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Je===e&&(_e&a)===a&&(nt===4||nt===3&&(_e&62914560)===_e&&300>Ht()-Cc?(qe&2)===0&&Ln(e,0):_c|=a,zn===_e&&(zn=0)),Ea(e)}function kh(e,t){t===0&&(t=pu()),e=vn(e,t),e!==null&&(Nl(e,t),Ea(e))}function Pp(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),kh(e,a)}function Ip(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,u=e.memoizedState;u!==null&&(a=u.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(t),kh(e,a)}function ev(e,t){return Jn(e,t)}var sr=null,Hn=null,Gc=!1,fr=!1,Xc=!1,Jl=0;function Ea(e){e!==Hn&&e.next===null&&(Hn===null?sr=Hn=e:Hn=Hn.next=e),fr=!0,Gc||(Gc=!0,av())}function zi(e,t){if(!Xc&&fr){Xc=!0;do for(var a=!1,l=sr;l!==null;){if(e!==0){var u=l.pendingLanes;if(u===0)var c=0;else{var h=l.suspendedLanes,v=l.pingedLanes;c=(1<<31-gt(42|e)+1)-1,c&=u&~(h&~v),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Wh(l,c))}else c=_e,c=Ml(l,l===Je?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||ya(l,c)||(a=!0,Wh(l,c));l=l.next}while(a);Xc=!1}}function tv(){Jh()}function Jh(){fr=Gc=!1;var e=0;Jl!==0&&(sv()&&(e=Jl),Jl=0);for(var t=Ht(),a=null,l=sr;l!==null;){var u=l.next,c=Fh(l,t);c===0?(l.next=null,a===null?sr=u:a.next=u,u===null&&(Hn=a)):(a=l,(e!==0||(c&3)!==0)&&(fr=!0)),l=u}zi(e)}function Fh(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var h=31-gt(c),v=1<<h,S=u[h];S===-1?((v&a)===0||(v&l)!==0)&&(u[h]=yu(v,t)):S<=t&&(e.expiredLanes|=v),c&=~v}if(t=Je,a=_e,a=Ml(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Ye===2||Ye===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&ua(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ya(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&ua(l),Wn(a)){case 2:case 8:a=du;break;case 32:a=nn;break;case 268435456:a=Fa;break;default:a=nn}return l=$h.bind(null,e),a=Jn(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&ua(l),e.callbackPriority=2,e.callbackNode=null,2}function $h(e,t){if(St!==0&&St!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(cr()&&e.callbackNode!==a)return null;var l=_e;return l=Ml(e,e===Je?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(zh(e,l,t),Fh(e,Ht()),e.callbackNode!=null&&e.callbackNode===a?$h.bind(null,e):null)}function Wh(e,t){if(cr())return null;zh(e,t,!0)}function av(){dv(function(){(qe&6)!==0?Jn(fu,tv):Jh()})}function Vc(){return Jl===0&&(Jl=un()),Jl}function Ph(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:xu(""+e)}function Ih(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function lv(e,t,a,l,u){if(t==="submit"&&a&&a.stateNode===u){var c=Ph((u[k]||null).action),h=l.submitter;h&&(t=(t=h[k]||null)?Ph(t.formAction):h.getAttribute("formAction"),t!==null&&(c=t,h=null));var v=new Au("action","action",null,l,u);e.push({event:v,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Jl!==0){var S=h?Ih(u,h):new FormData(u);cc(a,{pending:!0,data:S,method:u.method,action:c},null,S)}}else typeof c=="function"&&(v.preventDefault(),S=h?Ih(u,h):new FormData(u),cc(a,{pending:!0,data:S,method:u.method,action:c},c,S))},currentTarget:u}]})}}for(var Qc=0;Qc<Do.length;Qc++){var Zc=Do[Qc],nv=Zc.toLowerCase(),iv=Zc[0].toUpperCase()+Zc.slice(1);oa(nv,"on"+iv)}oa(zf,"onAnimationEnd"),oa(Cf,"onAnimationIteration"),oa(Uf,"onAnimationStart"),oa("dblclick","onDoubleClick"),oa("focusin","onFocus"),oa("focusout","onBlur"),oa(xp,"onTransitionRun"),oa(Rp,"onTransitionStart"),oa(Tp,"onTransitionCancel"),oa(Lf,"onTransitionEnd"),At("onMouseEnter",["mouseout","mouseover"]),At("onMouseLeave",["mouseout","mouseover"]),At("onPointerEnter",["pointerout","pointerover"]),At("onPointerLeave",["pointerout","pointerover"]),Bt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Bt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Bt("onBeforeInput",["compositionend","keypress","textInput","paste"]),Bt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Bt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Bt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ci="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),uv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ci));function em(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],u=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var h=l.length-1;0<=h;h--){var v=l[h],S=v.instance,N=v.currentTarget;if(v=v.listener,S!==c&&u.isPropagationStopped())break e;c=v,u.currentTarget=N;try{c(u)}catch(Y){Pu(Y)}u.currentTarget=null,c=S}else for(h=0;h<l.length;h++){if(v=l[h],S=v.instance,N=v.currentTarget,v=v.listener,S!==c&&u.isPropagationStopped())break e;c=v,u.currentTarget=N;try{c(u)}catch(Y){Pu(Y)}u.currentTarget=null,c=S}}}}function Me(e,t){var a=t[re];a===void 0&&(a=t[re]=new Set);var l=e+"__bubble";a.has(l)||(tm(t,e,2,!1),a.add(l))}function Kc(e,t,a){var l=0;t&&(l|=4),tm(a,e,l,t)}var dr="_reactListening"+Math.random().toString(36).slice(2);function kc(e){if(!e[dr]){e[dr]=!0,Ge.forEach(function(a){a!=="selectionchange"&&(uv.has(a)||Kc(a,!1,e),Kc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[dr]||(t[dr]=!0,Kc("selectionchange",!1,t))}}function tm(e,t,a,l){switch(Om(t)){case 2:var u=Cv;break;case 8:u=Uv;break;default:u=rs}a=u.bind(null,t,a,e),u=void 0,!yo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),l?u!==void 0?e.addEventListener(t,a,{capture:!0,passive:u}):e.addEventListener(t,a,!0):u!==void 0?e.addEventListener(t,a,{passive:u}):e.addEventListener(t,a,!1)}function Jc(e,t,a,l,u){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var v=l.stateNode.containerInfo;if(v===u)break;if(h===4)for(h=l.return;h!==null;){var S=h.tag;if((S===3||S===4)&&h.stateNode.containerInfo===u)return;h=h.return}for(;v!==null;){if(h=Re(v),h===null)return;if(S=h.tag,S===5||S===6||S===26||S===27){l=c=h;continue e}v=v.parentNode}}l=l.return}of(function(){var N=c,Y=ho(a),V=[];e:{var z=jf.get(e);if(z!==void 0){var L=Au,ve=e;switch(e){case"keypress":if(Tu(a)===0)break e;case"keydown":case"keyup":L=ep;break;case"focusin":ve="focus",L=bo;break;case"focusout":ve="blur",L=bo;break;case"beforeblur":case"afterblur":L=bo;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=X0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=lp;break;case zf:case Cf:case Uf:L=Z0;break;case Lf:L=ip;break;case"scroll":case"scrollend":L=Y0;break;case"wheel":L=rp;break;case"copy":case"cut":case"paste":L=k0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=hf;break;case"toggle":case"beforetoggle":L=cp}var de=(t&4)!==0,Qe=!de&&(e==="scroll"||e==="scrollend"),A=de?z!==null?z+"Capture":null:z;de=[];for(var R=N,D;R!==null;){var G=R;if(D=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||D===null||A===null||(G=Pn(R,A),G!=null&&de.push(Ui(R,G,D))),Qe)break;R=R.return}0<de.length&&(z=new L(z,ve,null,a,Y),V.push({event:z,listeners:de}))}}if((t&7)===0){e:{if(z=e==="mouseover"||e==="pointerover",L=e==="mouseout"||e==="pointerout",z&&a!==fo&&(ve=a.relatedTarget||a.fromElement)&&(Re(ve)||ve[ae]))break e;if((L||z)&&(z=Y.window===Y?Y:(z=Y.ownerDocument)?z.defaultView||z.parentWindow:window,L?(ve=a.relatedTarget||a.toElement,L=N,ve=ve?Re(ve):null,ve!==null&&(Qe=f(ve),de=ve.tag,ve!==Qe||de!==5&&de!==27&&de!==6)&&(ve=null)):(L=null,ve=N),L!==ve)){if(de=ff,G="onMouseLeave",A="onMouseEnter",R="mouse",(e==="pointerout"||e==="pointerover")&&(de=hf,G="onPointerLeave",A="onPointerEnter",R="pointer"),Qe=L==null?z:$e(L),D=ve==null?z:$e(ve),z=new de(G,R+"leave",L,a,Y),z.target=Qe,z.relatedTarget=D,G=null,Re(Y)===N&&(de=new de(A,R+"enter",ve,a,Y),de.target=D,de.relatedTarget=Qe,G=de),Qe=G,L&&ve)t:{for(de=L,A=ve,R=0,D=de;D;D=Bn(D))R++;for(D=0,G=A;G;G=Bn(G))D++;for(;0<R-D;)de=Bn(de),R--;for(;0<D-R;)A=Bn(A),D--;for(;R--;){if(de===A||A!==null&&de===A.alternate)break t;de=Bn(de),A=Bn(A)}de=null}else de=null;L!==null&&am(V,z,L,de,!1),ve!==null&&Qe!==null&&am(V,Qe,ve,de,!0)}}e:{if(z=N?$e(N):window,L=z.nodeName&&z.nodeName.toLowerCase(),L==="select"||L==="input"&&z.type==="file")var te=Ef;else if(bf(z))if(xf)te=bp;else{te=vp;var Ae=pp}else L=z.nodeName,!L||L.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?N&&so(N.elementType)&&(te=Ef):te=gp;if(te&&(te=te(e,N))){Sf(V,te,a,Y);break e}Ae&&Ae(e,z,N),e==="focusout"&&N&&z.type==="number"&&N.memoizedProps.value!=null&&co(z,"number",z.value)}switch(Ae=N?$e(N):window,e){case"focusin":(bf(Ae)||Ae.contentEditable==="true")&&(mn=Ae,Oo=N,ui=null);break;case"focusout":ui=Oo=mn=null;break;case"mousedown":Ao=!0;break;case"contextmenu":case"mouseup":case"dragend":Ao=!1,Nf(V,a,Y);break;case"selectionchange":if(Ep)break;case"keydown":case"keyup":Nf(V,a,Y)}var oe;if(Eo)e:{switch(e){case"compositionstart":var me="onCompositionStart";break e;case"compositionend":me="onCompositionEnd";break e;case"compositionupdate":me="onCompositionUpdate";break e}me=void 0}else hn?vf(e,a)&&(me="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(me="onCompositionStart");me&&(mf&&a.locale!=="ko"&&(hn||me!=="onCompositionStart"?me==="onCompositionEnd"&&hn&&(oe=cf()):(Pa=Y,po="value"in Pa?Pa.value:Pa.textContent,hn=!0)),Ae=hr(N,me),0<Ae.length&&(me=new df(me,e,null,a,Y),V.push({event:me,listeners:Ae}),oe?me.data=oe:(oe=gf(a),oe!==null&&(me.data=oe)))),(oe=fp?dp(e,a):hp(e,a))&&(me=hr(N,"onBeforeInput"),0<me.length&&(Ae=new df("onBeforeInput","beforeinput",null,a,Y),V.push({event:Ae,listeners:me}),Ae.data=oe)),lv(V,e,N,a,Y)}em(V,t)})}function Ui(e,t,a){return{instance:e,listener:t,currentTarget:a}}function hr(e,t){for(var a=t+"Capture",l=[];e!==null;){var u=e,c=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||c===null||(u=Pn(e,a),u!=null&&l.unshift(Ui(e,u,c)),u=Pn(e,t),u!=null&&l.push(Ui(e,u,c))),e.tag===3)return l;e=e.return}return[]}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function am(e,t,a,l,u){for(var c=t._reactName,h=[];a!==null&&a!==l;){var v=a,S=v.alternate,N=v.stateNode;if(v=v.tag,S!==null&&S===l)break;v!==5&&v!==26&&v!==27||N===null||(S=N,u?(N=Pn(a,c),N!=null&&h.unshift(Ui(a,N,S))):u||(N=Pn(a,c),N!=null&&h.push(Ui(a,N,S)))),a=a.return}h.length!==0&&e.push({event:t,listeners:h})}var rv=/\r\n?/g,ov=/\u0000|\uFFFD/g;function lm(e){return(typeof e=="string"?e:""+e).replace(rv,`
`).replace(ov,"")}function nm(e,t){return t=lm(t),lm(e)===t}function mr(){}function Ve(e,t,a,l,u,c){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||sn(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&sn(e,""+l);break;case"className":_a(e,"class",l);break;case"tabIndex":_a(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":_a(e,a,l);break;case"style":uf(e,l,c);break;case"data":if(t!=="object"){_a(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=xu(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&Ve(e,t,"name",u.name,u,null),Ve(e,t,"formEncType",u.formEncType,u,null),Ve(e,t,"formMethod",u.formMethod,u,null),Ve(e,t,"formTarget",u.formTarget,u,null)):(Ve(e,t,"encType",u.encType,u,null),Ve(e,t,"method",u.method,u,null),Ve(e,t,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=xu(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=mr);break;case"onScroll":l!=null&&Me("scroll",e);break;case"onScrollEnd":l!=null&&Me("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(u.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=xu(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":Me("beforetoggle",e),Me("toggle",e),Na(e,"popover",l);break;case"xlinkActuate":Se(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Se(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Se(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Se(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Se(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Se(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Se(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Se(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Se(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Na(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=B0.get(a)||a,Na(e,a,l))}}function Fc(e,t,a,l,u,c){switch(a){case"style":uf(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(u.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"children":typeof l=="string"?sn(e,l):(typeof l=="number"||typeof l=="bigint")&&sn(e,""+l);break;case"onScroll":l!=null&&Me("scroll",e);break;case"onScrollEnd":l!=null&&Me("scrollend",e);break;case"onClick":l!=null&&(e.onclick=mr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!pa.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(u=a.endsWith("Capture"),t=a.slice(2,u?a.length-7:void 0),c=e[k]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,u),typeof l=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,u);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):Na(e,a,l)}}}function Et(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Me("error",e),Me("load",e);var l=!1,u=!1,c;for(c in a)if(a.hasOwnProperty(c)){var h=a[c];if(h!=null)switch(c){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Ve(e,t,c,h,a,null)}}u&&Ve(e,t,"srcSet",a.srcSet,a,null),l&&Ve(e,t,"src",a.src,a,null);return;case"input":Me("invalid",e);var v=c=h=u=null,S=null,N=null;for(l in a)if(a.hasOwnProperty(l)){var Y=a[l];if(Y!=null)switch(l){case"name":u=Y;break;case"type":h=Y;break;case"checked":S=Y;break;case"defaultChecked":N=Y;break;case"value":c=Y;break;case"defaultValue":v=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(o(137,t));break;default:Ve(e,t,l,Y,a,null)}}tf(e,c,v,S,N,h,u,!1),Su(e);return;case"select":Me("invalid",e),l=h=c=null;for(u in a)if(a.hasOwnProperty(u)&&(v=a[u],v!=null))switch(u){case"value":c=v;break;case"defaultValue":h=v;break;case"multiple":l=v;default:Ve(e,t,u,v,a,null)}t=c,a=h,e.multiple=!!l,t!=null?cn(e,!!l,t,!1):a!=null&&cn(e,!!l,a,!0);return;case"textarea":Me("invalid",e),c=u=l=null;for(h in a)if(a.hasOwnProperty(h)&&(v=a[h],v!=null))switch(h){case"value":l=v;break;case"defaultValue":u=v;break;case"children":c=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(o(91));break;default:Ve(e,t,h,v,a,null)}lf(e,l,u,c),Su(e);return;case"option":for(S in a)if(a.hasOwnProperty(S)&&(l=a[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ve(e,t,S,l,a,null)}return;case"dialog":Me("beforetoggle",e),Me("toggle",e),Me("cancel",e),Me("close",e);break;case"iframe":case"object":Me("load",e);break;case"video":case"audio":for(l=0;l<Ci.length;l++)Me(Ci[l],e);break;case"image":Me("error",e),Me("load",e);break;case"details":Me("toggle",e);break;case"embed":case"source":case"link":Me("error",e),Me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in a)if(a.hasOwnProperty(N)&&(l=a[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Ve(e,t,N,l,a,null)}return;default:if(so(t)){for(Y in a)a.hasOwnProperty(Y)&&(l=a[Y],l!==void 0&&Fc(e,t,Y,l,a,void 0));return}}for(v in a)a.hasOwnProperty(v)&&(l=a[v],l!=null&&Ve(e,t,v,l,a,null))}function cv(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,c=null,h=null,v=null,S=null,N=null,Y=null;for(L in a){var V=a[L];if(a.hasOwnProperty(L)&&V!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":S=V;default:l.hasOwnProperty(L)||Ve(e,t,L,null,l,V)}}for(var z in l){var L=l[z];if(V=a[z],l.hasOwnProperty(z)&&(L!=null||V!=null))switch(z){case"type":c=L;break;case"name":u=L;break;case"checked":N=L;break;case"defaultChecked":Y=L;break;case"value":h=L;break;case"defaultValue":v=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(o(137,t));break;default:L!==V&&Ve(e,t,z,L,l,V)}}oo(e,h,v,S,N,Y,c,u);return;case"select":L=h=v=z=null;for(c in a)if(S=a[c],a.hasOwnProperty(c)&&S!=null)switch(c){case"value":break;case"multiple":L=S;default:l.hasOwnProperty(c)||Ve(e,t,c,null,l,S)}for(u in l)if(c=l[u],S=a[u],l.hasOwnProperty(u)&&(c!=null||S!=null))switch(u){case"value":z=c;break;case"defaultValue":v=c;break;case"multiple":h=c;default:c!==S&&Ve(e,t,u,c,l,S)}t=v,a=h,l=L,z!=null?cn(e,!!a,z,!1):!!l!=!!a&&(t!=null?cn(e,!!a,t,!0):cn(e,!!a,a?[]:"",!1));return;case"textarea":L=z=null;for(v in a)if(u=a[v],a.hasOwnProperty(v)&&u!=null&&!l.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:Ve(e,t,v,null,l,u)}for(h in l)if(u=l[h],c=a[h],l.hasOwnProperty(h)&&(u!=null||c!=null))switch(h){case"value":z=u;break;case"defaultValue":L=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(o(91));break;default:u!==c&&Ve(e,t,h,u,l,c)}af(e,z,L);return;case"option":for(var ve in a)if(z=a[ve],a.hasOwnProperty(ve)&&z!=null&&!l.hasOwnProperty(ve))switch(ve){case"selected":e.selected=!1;break;default:Ve(e,t,ve,null,l,z)}for(S in l)if(z=l[S],L=a[S],l.hasOwnProperty(S)&&z!==L&&(z!=null||L!=null))switch(S){case"selected":e.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Ve(e,t,S,z,l,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var de in a)z=a[de],a.hasOwnProperty(de)&&z!=null&&!l.hasOwnProperty(de)&&Ve(e,t,de,null,l,z);for(N in l)if(z=l[N],L=a[N],l.hasOwnProperty(N)&&z!==L&&(z!=null||L!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(o(137,t));break;default:Ve(e,t,N,z,l,L)}return;default:if(so(t)){for(var Qe in a)z=a[Qe],a.hasOwnProperty(Qe)&&z!==void 0&&!l.hasOwnProperty(Qe)&&Fc(e,t,Qe,void 0,l,z);for(Y in l)z=l[Y],L=a[Y],!l.hasOwnProperty(Y)||z===L||z===void 0&&L===void 0||Fc(e,t,Y,z,l,L);return}}for(var A in a)z=a[A],a.hasOwnProperty(A)&&z!=null&&!l.hasOwnProperty(A)&&Ve(e,t,A,null,l,z);for(V in l)z=l[V],L=a[V],!l.hasOwnProperty(V)||z===L||z==null&&L==null||Ve(e,t,V,z,l,L)}var $c=null,Wc=null;function yr(e){return e.nodeType===9?e:e.ownerDocument}function im(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function um(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Pc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ic=null;function sv(){var e=window.event;return e&&e.type==="popstate"?e===Ic?!1:(Ic=e,!0):(Ic=null,!1)}var rm=typeof setTimeout=="function"?setTimeout:void 0,fv=typeof clearTimeout=="function"?clearTimeout:void 0,om=typeof Promise=="function"?Promise:void 0,dv=typeof queueMicrotask=="function"?queueMicrotask:typeof om<"u"?function(e){return om.resolve(null).then(e).catch(hv)}:rm;function hv(e){setTimeout(function(){throw e})}function ml(e){return e==="head"}function cm(e,t){var a=t,l=0,u=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<l&&8>l){a=l;var h=e.ownerDocument;if(a&1&&Li(h.documentElement),a&2&&Li(h.body),a&4)for(a=h.head,Li(a),h=a.firstChild;h;){var v=h.nextSibling,S=h.nodeName;h[ne]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&h.rel.toLowerCase()==="stylesheet"||a.removeChild(h),h=v}}if(u===0){e.removeChild(c),Vi(t);return}u--}else a==="$"||a==="$?"||a==="$!"?u++:l=a.charCodeAt(0)-48;else l=0;a=c}while(a);Vi(t)}function es(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":es(a),ie(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function mv(e,t,a,l){for(;e.nodeType===1;){var u=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[ne])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=sa(e.nextSibling),e===null)break}return null}function yv(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=sa(e.nextSibling),e===null))return null;return e}function ts(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function pv(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function sa(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var as=null;function sm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function fm(e,t,a){switch(t=yr(a),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Li(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ie(e)}var aa=new Map,dm=new Set;function pr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ka=W.d;W.d={f:vv,r:gv,D:bv,C:Sv,L:Ev,m:xv,X:Tv,S:Rv,M:Ov};function vv(){var e=Ka.f(),t=rr();return e||t}function gv(e){var t=je(e);t!==null&&t.tag===5&&t.type==="form"?zd(t):Ka.r(e)}var qn=typeof document>"u"?null:document;function hm(e,t,a){var l=qn;if(l&&typeof t=="string"&&t){var u=Ft(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof a=="string"&&(u+='[crossorigin="'+a+'"]'),dm.has(u)||(dm.add(u),e={rel:e,crossOrigin:a,href:t},l.querySelector(u)===null&&(t=l.createElement("link"),Et(t,"link",e),be(t),l.head.appendChild(t)))}}function bv(e){Ka.D(e),hm("dns-prefetch",e,null)}function Sv(e,t){Ka.C(e,t),hm("preconnect",e,t)}function Ev(e,t,a){Ka.L(e,t,a);var l=qn;if(l&&e&&t){var u='link[rel="preload"][as="'+Ft(t)+'"]';t==="image"&&a&&a.imageSrcSet?(u+='[imagesrcset="'+Ft(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(u+='[imagesizes="'+Ft(a.imageSizes)+'"]')):u+='[href="'+Ft(e)+'"]';var c=u;switch(t){case"style":c=Yn(e);break;case"script":c=Gn(e)}aa.has(c)||(e=g({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),aa.set(c,e),l.querySelector(u)!==null||t==="style"&&l.querySelector(ji(c))||t==="script"&&l.querySelector(Hi(c))||(t=l.createElement("link"),Et(t,"link",e),be(t),l.head.appendChild(t)))}}function xv(e,t){Ka.m(e,t);var a=qn;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+Ft(l)+'"][href="'+Ft(e)+'"]',c=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Gn(e)}if(!aa.has(c)&&(e=g({rel:"modulepreload",href:e},t),aa.set(c,e),a.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Hi(c)))return}l=a.createElement("link"),Et(l,"link",e),be(l),a.head.appendChild(l)}}}function Rv(e,t,a){Ka.S(e,t,a);var l=qn;if(l&&e){var u=at(l).hoistableStyles,c=Yn(e);t=t||"default";var h=u.get(c);if(!h){var v={loading:0,preload:null};if(h=l.querySelector(ji(c)))v.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":t},a),(a=aa.get(c))&&ls(e,a);var S=h=l.createElement("link");be(S),Et(S,"link",e),S._p=new Promise(function(N,Y){S.onload=N,S.onerror=Y}),S.addEventListener("load",function(){v.loading|=1}),S.addEventListener("error",function(){v.loading|=2}),v.loading|=4,vr(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:v},u.set(c,h)}}}function Tv(e,t){Ka.X(e,t);var a=qn;if(a&&e){var l=at(a).hoistableScripts,u=Gn(e),c=l.get(u);c||(c=a.querySelector(Hi(u)),c||(e=g({src:e,async:!0},t),(t=aa.get(u))&&ns(e,t),c=a.createElement("script"),be(c),Et(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function Ov(e,t){Ka.M(e,t);var a=qn;if(a&&e){var l=at(a).hoistableScripts,u=Gn(e),c=l.get(u);c||(c=a.querySelector(Hi(u)),c||(e=g({src:e,async:!0,type:"module"},t),(t=aa.get(u))&&ns(e,t),c=a.createElement("script"),be(c),Et(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(u,c))}}function mm(e,t,a,l){var u=(u=he.current)?pr(u):null;if(!u)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Yn(a.href),a=at(u).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Yn(a.href);var c=at(u).hoistableStyles,h=c.get(e);if(h||(u=u.ownerDocument||u,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,h),(c=u.querySelector(ji(e)))&&!c._p&&(h.instance=c,h.state.loading=5),aa.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},aa.set(e,a),c||Av(u,e,a,h.state))),t&&l===null)throw Error(o(528,""));return h}if(t&&l!==null)throw Error(o(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Gn(a),a=at(u).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Yn(e){return'href="'+Ft(e)+'"'}function ji(e){return'link[rel="stylesheet"]['+e+"]"}function ym(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function Av(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Et(t,"link",a),be(t),e.head.appendChild(t))}function Gn(e){return'[src="'+Ft(e)+'"]'}function Hi(e){return"script[async]"+e}function pm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Ft(a.href)+'"]');if(l)return t.instance=l,be(l),l;var u=g({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),be(l),Et(l,"style",u),vr(l,a.precedence,e),t.instance=l;case"stylesheet":u=Yn(a.href);var c=e.querySelector(ji(u));if(c)return t.state.loading|=4,t.instance=c,be(c),c;l=ym(a),(u=aa.get(u))&&ls(l,u),c=(e.ownerDocument||e).createElement("link"),be(c);var h=c;return h._p=new Promise(function(v,S){h.onload=v,h.onerror=S}),Et(c,"link",l),t.state.loading|=4,vr(c,a.precedence,e),t.instance=c;case"script":return c=Gn(a.src),(u=e.querySelector(Hi(c)))?(t.instance=u,be(u),u):(l=a,(u=aa.get(c))&&(l=g({},a),ns(l,u)),e=e.ownerDocument||e,u=e.createElement("script"),be(u),Et(u,"link",l),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,vr(l,a.precedence,e));return t.instance}function vr(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,c=u,h=0;h<l.length;h++){var v=l[h];if(v.dataset.precedence===t)c=v;else if(c!==u)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function ls(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ns(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var gr=null;function vm(e,t,a){if(gr===null){var l=new Map,u=gr=new Map;u.set(a,l)}else u=gr,l=u.get(a),l||(l=new Map,u.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),u=0;u<a.length;u++){var c=a[u];if(!(c[ne]||c[Z]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var h=c.getAttribute(t)||"";h=e+h;var v=l.get(h);v?v.push(c):l.set(h,[c])}}return l}function gm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function wv(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function bm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Bi=null;function Dv(){}function Mv(e,t,a){if(Bi===null)throw Error(o(475));var l=Bi;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Yn(a.href),c=e.querySelector(ji(u));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=br.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,be(c);return}c=e.ownerDocument||e,a=ym(a),(u=aa.get(u))&&ls(a,u),c=c.createElement("link"),be(c);var h=c;h._p=new Promise(function(v,S){h.onload=v,h.onerror=S}),Et(c,"link",a),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=br.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Nv(){if(Bi===null)throw Error(o(475));var e=Bi;return e.stylesheets&&e.count===0&&is(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&is(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function br(){if(this.count--,this.count===0){if(this.stylesheets)is(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Sr=null;function is(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Sr=new Map,t.forEach(_v,e),Sr=null,br.call(e))}function _v(e,t){if(!(t.state.loading&4)){var a=Sr.get(e);if(a)var l=a.get(null);else{a=new Map,Sr.set(e,a);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<u.length;c++){var h=u[c];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(a.set(h.dataset.precedence,h),l=h)}l&&a.set(null,l)}u=t.instance,h=u.getAttribute("data-precedence"),c=a.get(h)||l,c===l&&a.set(null,u),a.set(h,u),this.count++,l=br.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),c?c.parentNode.insertBefore(u,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var qi={$$typeof:F,Provider:null,Consumer:null,_currentValue:ee,_currentValue2:ee,_threadCount:0};function zv(e,t,a,l,u,c,h,v){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=rn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=rn(0),this.hiddenUpdates=rn(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=c,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function Sm(e,t,a,l,u,c,h,v,S,N,Y,V){return e=new zv(e,t,a,h,v,S,N,V),t=1,c===!0&&(t|=24),c=Yt(3,null,null,t),e.current=c,c.stateNode=e,t=Yo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:a,cache:t},Qo(c),e}function Em(e){return e?(e=gn,e):gn}function xm(e,t,a,l,u,c){u=Em(u),l.context===null?l.context=u:l.pendingContext=u,l=tl(t),l.payload={element:a},c=c===void 0?null:c,c!==null&&(l.callback=c),a=al(e,l,t),a!==null&&(Zt(a,e,t),yi(a,e,t))}function Rm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function us(e,t){Rm(e,t),(e=e.alternate)&&Rm(e,t)}function Tm(e){if(e.tag===13){var t=vn(e,67108864);t!==null&&Zt(t,e,67108864),us(e,67108864)}}var Er=!0;function Cv(e,t,a,l){var u=C.T;C.T=null;var c=W.p;try{W.p=2,rs(e,t,a,l)}finally{W.p=c,C.T=u}}function Uv(e,t,a,l){var u=C.T;C.T=null;var c=W.p;try{W.p=8,rs(e,t,a,l)}finally{W.p=c,C.T=u}}function rs(e,t,a,l){if(Er){var u=os(l);if(u===null)Jc(e,t,l,xr,a),Am(e,l);else if(jv(u,e,t,a,l))l.stopPropagation();else if(Am(e,l),t&4&&-1<Lv.indexOf(e)){for(;u!==null;){var c=je(u);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var h=Da(c.pendingLanes);if(h!==0){var v=c;for(v.pendingLanes|=2,v.entangledLanes|=2;h;){var S=1<<31-gt(h);v.entanglements[1]|=S,h&=~S}Ea(c),(qe&6)===0&&(ir=Ht()+500,zi(0))}}break;case 13:v=vn(c,2),v!==null&&Zt(v,c,2),rr(),us(c,2)}if(c=os(l),c===null&&Jc(e,t,l,xr,a),c===u)break;u=c}u!==null&&l.stopPropagation()}else Jc(e,t,l,null,a)}}function os(e){return e=ho(e),cs(e)}var xr=null;function cs(e){if(xr=null,e=Re(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=d(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return xr=e,null}function Om(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(uo()){case fu:return 2;case du:return 8;case nn:case wa:return 32;case Fa:return 268435456;default:return 32}default:return 32}}var ss=!1,yl=null,pl=null,vl=null,Yi=new Map,Gi=new Map,gl=[],Lv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Am(e,t){switch(e){case"focusin":case"focusout":yl=null;break;case"dragenter":case"dragleave":pl=null;break;case"mouseover":case"mouseout":vl=null;break;case"pointerover":case"pointerout":Yi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Gi.delete(t.pointerId)}}function Xi(e,t,a,l,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:c,targetContainers:[u]},t!==null&&(t=je(t),t!==null&&Tm(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function jv(e,t,a,l,u){switch(t){case"focusin":return yl=Xi(yl,e,t,a,l,u),!0;case"dragenter":return pl=Xi(pl,e,t,a,l,u),!0;case"mouseover":return vl=Xi(vl,e,t,a,l,u),!0;case"pointerover":var c=u.pointerId;return Yi.set(c,Xi(Yi.get(c)||null,e,t,a,l,u)),!0;case"gotpointercapture":return c=u.pointerId,Gi.set(c,Xi(Gi.get(c)||null,e,t,a,l,u)),!0}return!1}function wm(e){var t=Re(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=d(a),t!==null){e.blockedOn=t,w(e.priority,function(){if(a.tag===13){var l=Qt();l=$n(l);var u=vn(a,l);u!==null&&Zt(u,a,l),us(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Rr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=os(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);fo=l,a.target.dispatchEvent(l),fo=null}else return t=je(a),t!==null&&Tm(t),e.blockedOn=a,!1;t.shift()}return!0}function Dm(e,t,a){Rr(e)&&a.delete(t)}function Hv(){ss=!1,yl!==null&&Rr(yl)&&(yl=null),pl!==null&&Rr(pl)&&(pl=null),vl!==null&&Rr(vl)&&(vl=null),Yi.forEach(Dm),Gi.forEach(Dm)}function Tr(e,t){e.blockedOn===t&&(e.blockedOn=null,ss||(ss=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Hv)))}var Or=null;function Mm(e){Or!==e&&(Or=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Or===e&&(Or=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],u=e[t+2];if(typeof l!="function"){if(cs(l||a)===null)continue;break}var c=je(a);c!==null&&(e.splice(t,3),t-=3,cc(c,{pending:!0,data:u,method:a.method,action:l},l,u))}}))}function Vi(e){function t(S){return Tr(S,e)}yl!==null&&Tr(yl,e),pl!==null&&Tr(pl,e),vl!==null&&Tr(vl,e),Yi.forEach(t),Gi.forEach(t);for(var a=0;a<gl.length;a++){var l=gl[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<gl.length&&(a=gl[0],a.blockedOn===null);)wm(a),a.blockedOn===null&&gl.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var u=a[l],c=a[l+1],h=u[k]||null;if(typeof c=="function")h||Mm(a);else if(h){var v=null;if(c&&c.hasAttribute("formAction")){if(u=c,h=c[k]||null)v=h.formAction;else if(cs(u)!==null)continue}else v=h.action;typeof v=="function"?a[l+1]=v:(a.splice(l,3),l-=3),Mm(a)}}}function fs(e){this._internalRoot=e}Ar.prototype.render=fs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var a=t.current,l=Qt();xm(a,l,e,t,null,null)},Ar.prototype.unmount=fs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xm(e.current,2,null,e,null,null),rr(),t[ae]=null}};function Ar(e){this._internalRoot=e}Ar.prototype.unstable_scheduleHydration=function(e){if(e){var t=x();e={blockedOn:null,target:e,priority:t};for(var a=0;a<gl.length&&t!==0&&t<gl[a].priority;a++);gl.splice(a,0,e),a===0&&wm(e)}};var Nm=i.version;if(Nm!=="19.1.1")throw Error(o(527,Nm,"19.1.1"));W.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var Bv={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wr.isDisabled&&wr.supportsFiber)try{Dt=wr.inject(Bv),Ie=wr}catch{}}return Zi.createRoot=function(e,t){if(!s(e))throw Error(o(299));var a=!1,l="",u=Kd,c=kd,h=Jd,v=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(v=t.unstable_transitionCallbacks)),t=Sm(e,1,!1,null,null,a,l,u,c,h,v,null),e[ae]=t.current,kc(e),new fs(t)},Zi.hydrateRoot=function(e,t,a){if(!s(e))throw Error(o(299));var l=!1,u="",c=Kd,h=kd,v=Jd,S=null,N=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(u=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(h=a.onCaughtError),a.onRecoverableError!==void 0&&(v=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(S=a.unstable_transitionCallbacks),a.formState!==void 0&&(N=a.formState)),t=Sm(e,1,!0,t,a??null,l,u,c,h,v,S,N),t.context=Em(null),a=t.current,l=Qt(),l=$n(l),u=tl(l),u.callback=null,al(a,u,l),a=l,t.current.lanes=a,Nl(t,a),Ea(t),e[ae]=t.current,kc(e),new Ar(t)},Zi.version="19.1.1",Zi}var Ym;function Fv(){if(Ym)return ms.exports;Ym=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}return n(),ms.exports=Jv(),ms.exports}var $v=Fv();/**
 * react-router v7.8.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Ty=n=>{throw TypeError(n)},Wv=(n,i,r)=>i.has(n)||Ty("Cannot "+r),gs=(n,i,r)=>(Wv(n,i,"read from private field"),r?r.call(n):i.get(n)),Pv=(n,i,r)=>i.has(n)?Ty("Cannot add the same private member more than once"):i instanceof WeakSet?i.add(n):i.set(n,r),Gm="popstate";function Iv(n={}){function i(o,s){let{pathname:f,search:d,hash:y}=o.location;return Wi("",{pathname:f,search:d,hash:y},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function r(o,s){return typeof s=="string"?s:Tl(s)}return tg(i,r,null,n)}function Ne(n,i){if(n===!1||n===null||typeof n>"u")throw new Error(i)}function dt(n,i){if(!n){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function eg(){return Math.random().toString(36).substring(2,10)}function Xm(n,i){return{usr:n.state,key:n.key,idx:i}}function Wi(n,i,r=null,o){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof i=="string"?Ol(i):i,state:r,key:i&&i.key||o||eg()}}function Tl({pathname:n="/",search:i="",hash:r=""}){return i&&i!=="?"&&(n+=i.charAt(0)==="?"?i:"?"+i),r&&r!=="#"&&(n+=r.charAt(0)==="#"?r:"#"+r),n}function Ol(n){let i={};if(n){let r=n.indexOf("#");r>=0&&(i.hash=n.substring(r),n=n.substring(0,r));let o=n.indexOf("?");o>=0&&(i.search=n.substring(o),n=n.substring(0,o)),n&&(i.pathname=n)}return i}function tg(n,i,r,o={}){let{window:s=document.defaultView,v5Compat:f=!1}=o,d=s.history,y="POP",p=null,m=g();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function g(){return(d.state||{idx:null}).idx}function b(){y="POP";let H=g(),K=H==null?null:H-m;m=H,p&&p({action:y,location:Q.location,delta:K})}function T(H,K){y="PUSH";let J=Wi(Q.location,H,K);m=g()+1;let F=Xm(J,m),ye=Q.createHref(J);try{d.pushState(F,"",ye)}catch($){if($ instanceof DOMException&&$.name==="DataCloneError")throw $;s.location.assign(ye)}f&&p&&p({action:y,location:Q.location,delta:1})}function M(H,K){y="REPLACE";let J=Wi(Q.location,H,K);m=g();let F=Xm(J,m),ye=Q.createHref(J);d.replaceState(F,"",ye),f&&p&&p({action:y,location:Q.location,delta:0})}function U(H){return Oy(H)}let Q={get action(){return y},get location(){return n(s,d)},listen(H){if(p)throw new Error("A history only accepts one active listener");return s.addEventListener(Gm,b),p=H,()=>{s.removeEventListener(Gm,b),p=null}},createHref(H){return i(s,H)},createURL:U,encodeLocation(H){let K=U(H);return{pathname:K.pathname,search:K.search,hash:K.hash}},push:T,replace:M,go(H){return d.go(H)}};return Q}function Oy(n,i=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Ne(r,"No window.location.(origin|href) available to create URL");let o=typeof n=="string"?n:Tl(n);return o=o.replace(/ $/,"%20"),!i&&o.startsWith("//")&&(o=r+o),new URL(o,r)}var $i,Vm=class{constructor(n){if(Pv(this,$i,new Map),n)for(let[i,r]of n)this.set(i,r)}get(n){if(gs(this,$i).has(n))return gs(this,$i).get(n);if(n.defaultValue!==void 0)return n.defaultValue;throw new Error("No value found for context")}set(n,i){gs(this,$i).set(n,i)}};$i=new WeakMap;var ag=new Set(["lazy","caseSensitive","path","id","index","children"]);function lg(n){return ag.has(n)}var ng=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function ig(n){return ng.has(n)}function ug(n){return n.index===!0}function Pi(n,i,r=[],o={},s=!1){return n.map((f,d)=>{let y=[...r,String(d)],p=typeof f.id=="string"?f.id:y.join("-");if(Ne(f.index!==!0||!f.children,"Cannot specify children on an index route"),Ne(s||!o[p],`Found a route id collision on id "${p}".  Route id's must be globally unique within Data Router usages`),ug(f)){let m={...f,...i(f),id:p};return o[p]=m,m}else{let m={...f,...i(f),id:p,children:void 0};return o[p]=m,f.children&&(m.children=Pi(f.children,i,y,o,s)),m}})}function El(n,i,r="/"){return zr(n,i,r,!1)}function zr(n,i,r,o){let s=typeof i=="string"?Ol(i):i,f=na(s.pathname||"/",r);if(f==null)return null;let d=Ay(n);og(d);let y=null;for(let p=0;y==null&&p<d.length;++p){let m=bg(f);y=vg(d[p],m,o)}return y}function rg(n,i){let{route:r,pathname:o,params:s}=n;return{id:r.id,pathname:o,params:s,data:i[r.id],loaderData:i[r.id],handle:r.handle}}function Ay(n,i=[],r=[],o="",s=!1){let f=(d,y,p=s,m)=>{let g={relativePath:m===void 0?d.path||"":m,caseSensitive:d.caseSensitive===!0,childrenIndex:y,route:d};if(g.relativePath.startsWith("/")){if(!g.relativePath.startsWith(o)&&p)return;Ne(g.relativePath.startsWith(o),`Absolute route path "${g.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),g.relativePath=g.relativePath.slice(o.length)}let b=Ta([o,g.relativePath]),T=r.concat(g);d.children&&d.children.length>0&&(Ne(d.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${b}".`),Ay(d.children,i,T,b,p)),!(d.path==null&&!d.index)&&i.push({path:b,score:yg(b,d.index),routesMeta:T})};return n.forEach((d,y)=>{if(d.path===""||!d.path?.includes("?"))f(d,y);else for(let p of wy(d.path))f(d,y,!0,p)}),i}function wy(n){let i=n.split("/");if(i.length===0)return[];let[r,...o]=i,s=r.endsWith("?"),f=r.replace(/\?$/,"");if(o.length===0)return s?[f,""]:[f];let d=wy(o.join("/")),y=[];return y.push(...d.map(p=>p===""?f:[f,p].join("/"))),s&&y.push(...d),y.map(p=>n.startsWith("/")&&p===""?"/":p)}function og(n){n.sort((i,r)=>i.score!==r.score?r.score-i.score:pg(i.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}var cg=/^:[\w-]+$/,sg=3,fg=2,dg=1,hg=10,mg=-2,Qm=n=>n==="*";function yg(n,i){let r=n.split("/"),o=r.length;return r.some(Qm)&&(o+=mg),i&&(o+=fg),r.filter(s=>!Qm(s)).reduce((s,f)=>s+(cg.test(f)?sg:f===""?dg:hg),o)}function pg(n,i){return n.length===i.length&&n.slice(0,-1).every((o,s)=>o===i[s])?n[n.length-1]-i[i.length-1]:0}function vg(n,i,r=!1){let{routesMeta:o}=n,s={},f="/",d=[];for(let y=0;y<o.length;++y){let p=o[y],m=y===o.length-1,g=f==="/"?i:i.slice(f.length)||"/",b=Yr({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},g),T=p.route;if(!b&&m&&r&&!o[o.length-1].route.index&&(b=Yr({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},g)),!b)return null;Object.assign(s,b.params),d.push({params:s,pathname:Ta([f,b.pathname]),pathnameBase:Rg(Ta([f,b.pathnameBase])),route:T}),b.pathnameBase!=="/"&&(f=Ta([f,b.pathnameBase]))}return d}function Yr(n,i){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[r,o]=gg(n.path,n.caseSensitive,n.end),s=i.match(r);if(!s)return null;let f=s[0],d=f.replace(/(.)\/+$/,"$1"),y=s.slice(1);return{params:o.reduce((m,{paramName:g,isOptional:b},T)=>{if(g==="*"){let U=y[T]||"";d=f.slice(0,f.length-U.length).replace(/(.)\/+$/,"$1")}const M=y[T];return b&&!M?m[g]=void 0:m[g]=(M||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:n}}function gg(n,i=!1,r=!0){dt(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let o=[],s="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,y,p)=>(o.push({paramName:y,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)")).replace(/\/([\w-]+)\?(\/|$)/g,"(/$1)?$2");return n.endsWith("*")?(o.push({paramName:"*"}),s+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?s+="\\/*$":n!==""&&n!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,i?void 0:"i"),o]}function bg(n){try{return n.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return dt(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),n}}function na(n,i){if(i==="/")return n;if(!n.toLowerCase().startsWith(i.toLowerCase()))return null;let r=i.endsWith("/")?i.length-1:i.length,o=n.charAt(r);return o&&o!=="/"?null:n.slice(r)||"/"}function Sg({basename:n,pathname:i}){return i==="/"?n:Ta([n,i])}function Eg(n,i="/"){let{pathname:r,search:o="",hash:s=""}=typeof n=="string"?Ol(n):n;return{pathname:r?r.startsWith("/")?r:xg(r,i):i,search:Tg(o),hash:Og(s)}}function xg(n,i){let r=i.replace(/\/+$/,"").split("/");return n.split("/").forEach(s=>{s===".."?r.length>1&&r.pop():s!=="."&&r.push(s)}),r.length>1?r.join("/"):"/"}function bs(n,i,r,o){return`Cannot include a '${n}' character in a manually specified \`to.${i}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Dy(n){return n.filter((i,r)=>r===0||i.route.path&&i.route.path.length>0)}function qs(n){let i=Dy(n);return i.map((r,o)=>o===i.length-1?r.pathname:r.pathnameBase)}function Ys(n,i,r,o=!1){let s;typeof n=="string"?s=Ol(n):(s={...n},Ne(!s.pathname||!s.pathname.includes("?"),bs("?","pathname","search",s)),Ne(!s.pathname||!s.pathname.includes("#"),bs("#","pathname","hash",s)),Ne(!s.search||!s.search.includes("#"),bs("#","search","hash",s)));let f=n===""||s.pathname==="",d=f?"/":s.pathname,y;if(d==null)y=r;else{let b=i.length-1;if(!o&&d.startsWith("..")){let T=d.split("/");for(;T[0]==="..";)T.shift(),b-=1;s.pathname=T.join("/")}y=b>=0?i[b]:"/"}let p=Eg(s,y),m=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&r.endsWith("/");return!p.pathname.endsWith("/")&&(m||g)&&(p.pathname+="/"),p}var Ta=n=>n.join("/").replace(/\/\/+/g,"/"),Rg=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),Tg=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,Og=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n,Gr=class{constructor(n,i,r,o=!1){this.status=n,this.statusText=i||"",this.internal=o,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Ii(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var My=["POST","PUT","PATCH","DELETE"],Ag=new Set(My),wg=["GET",...My],Dg=new Set(wg),Mg=new Set([301,302,303,307,308]),Ng=new Set([307,308]),Ss={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},_g={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ki={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},zg=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Gs=n=>zg.test(n),Cg=n=>({hasErrorBoundary:!!n.hasErrorBoundary}),Ny="remix-router-transitions",_y=Symbol("ResetLoaderData");function Ug(n){const i=n.window?n.window:typeof window<"u"?window:void 0,r=typeof i<"u"&&typeof i.document<"u"&&typeof i.document.createElement<"u";Ne(n.routes.length>0,"You must provide a non-empty routes array to createRouter");let o=n.hydrationRouteProperties||[],s=n.mapRouteProperties||Cg,f={},d=Pi(n.routes,s,void 0,f),y,p=n.basename||"/";p.startsWith("/")||(p=`/${p}`);let m=n.dataStrategy||qg,g={...n.future},b=null,T=new Set,M=null,U=null,Q=null,H=n.hydrationData!=null,K=El(d,n.history.location,p),J=!1,F=null,ye;if(K==null&&!n.patchRoutesOnNavigation){let x=la(404,{pathname:n.history.location.pathname}),{matches:w,route:j}=Dr(d);ye=!0,K=w,F={[j.id]:x}}else if(K&&!n.hydrationData&&_l(K,d,n.history.location.pathname).active&&(K=null),K)if(K.some(x=>x.route.lazy))ye=!1;else if(!K.some(x=>x.route.loader))ye=!0;else{let x=n.hydrationData?n.hydrationData.loaderData:null,w=n.hydrationData?n.hydrationData.errors:null;if(w){let j=K.findIndex(Z=>w[Z.route.id]!==void 0);ye=K.slice(0,j+1).every(Z=>!ws(Z.route,x,w))}else ye=K.every(j=>!ws(j.route,x,w))}else{ye=!1,K=[];let x=_l(null,d,n.history.location.pathname);x.active&&x.matches&&(J=!0,K=x.matches)}let $,_={historyAction:n.history.action,location:n.history.location,matches:K,initialized:ye,navigation:Ss,restoreScrollPosition:n.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:n.hydrationData&&n.hydrationData.loaderData||{},actionData:n.hydrationData&&n.hydrationData.actionData||null,errors:n.hydrationData&&n.hydrationData.errors||F,fetchers:new Map,blockers:new Map},ce="POP",ge=!1,pe,Ue=!1,Pe=new Map,Fe=null,ze=!1,Le=!1,Be=new Set,C=new Map,W=0,ee=-1,Te=new Map,E=new Set,X=new Map,I=new Map,P=new Set,ue=new Map,we,he=null;function Ot(){if(b=n.history.listen(({action:x,location:w,delta:j})=>{if(we){we(),we=void 0;return}dt(ue.size===0||j!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let Z=yu({currentLocation:_.location,nextLocation:w,historyAction:x});if(Z&&j!=null){let k=new Promise(ae=>{we=ae});n.history.go(j*-1),ya(Z,{state:"blocked",location:w,proceed(){ya(Z,{state:"proceeding",proceed:void 0,reset:void 0,location:w}),k.then(()=>n.history.go(j))},reset(){let ae=new Map(_.blockers);ae.set(Z,Ki),ot({blockers:ae})}});return}return ua(x,w)}),r){eb(i,Pe);let x=()=>tb(i,Pe);i.addEventListener("pagehide",x),Fe=()=>i.removeEventListener("pagehide",x)}return _.initialized||ua("POP",_.location,{initialHydration:!0}),$}function ke(){b&&b(),Fe&&Fe(),T.clear(),pe&&pe.abort(),_.fetchers.forEach((x,w)=>gt(w)),_.blockers.forEach((x,w)=>Ml(w))}function ha(x){return T.add(x),()=>T.delete(x)}function ot(x,w={}){x.matches&&(x.matches=x.matches.map(k=>{let ae=f[k.route.id],re=k.route;return re.element!==ae.element||re.errorElement!==ae.errorElement||re.hydrateFallbackElement!==ae.hydrateFallbackElement?{...k,route:ae}:k})),_={..._,...x};let j=[],Z=[];_.fetchers.forEach((k,ae)=>{k.state==="idle"&&(P.has(ae)?j.push(ae):Z.push(ae))}),P.forEach(k=>{!_.fetchers.has(k)&&!C.has(k)&&j.push(k)}),[...T].forEach(k=>k(_,{deletedFetchers:j,viewTransitionOpts:w.viewTransitionOpts,flushSync:w.flushSync===!0})),j.forEach(k=>gt(k)),Z.forEach(k=>_.fetchers.delete(k))}function ia(x,w,{flushSync:j}={}){let Z=_.actionData!=null&&_.navigation.formMethod!=null&&Ut(_.navigation.formMethod)&&_.navigation.state==="loading"&&x.state?._isRedirect!==!0,k;w.actionData?Object.keys(w.actionData).length>0?k=w.actionData:k=null:Z?k=_.actionData:k=null;let ae=w.loaderData?ey(_.loaderData,w.loaderData,w.matches||[],w.errors):_.loaderData,re=_.blockers;re.size>0&&(re=new Map(re),re.forEach((ne,ie)=>re.set(ie,Ki)));let le=ze?!1:vu(x,w.matches||_.matches),se=ge===!0||_.navigation.formMethod!=null&&Ut(_.navigation.formMethod)&&x.state?._isRedirect!==!0;y&&(d=y,y=void 0),ze||ce==="POP"||(ce==="PUSH"?n.history.push(x,x.state):ce==="REPLACE"&&n.history.replace(x,x.state));let fe;if(ce==="POP"){let ne=Pe.get(_.location.pathname);ne&&ne.has(x.pathname)?fe={currentLocation:_.location,nextLocation:x}:Pe.has(x.pathname)&&(fe={currentLocation:x,nextLocation:_.location})}else if(Ue){let ne=Pe.get(_.location.pathname);ne?ne.add(x.pathname):(ne=new Set([x.pathname]),Pe.set(_.location.pathname,ne)),fe={currentLocation:_.location,nextLocation:x}}ot({...w,actionData:k,loaderData:ae,historyAction:ce,location:x,initialized:!0,navigation:Ss,revalidation:"idle",restoreScrollPosition:le,preventScrollReset:se,blockers:re},{viewTransitionOpts:fe,flushSync:j===!0}),ce="POP",ge=!1,Ue=!1,ze=!1,Le=!1,he?.resolve(),he=null}async function ln(x,w){if(typeof x=="number"){n.history.go(x);return}let j=As(_.location,_.matches,p,x,w?.fromRouteId,w?.relative),{path:Z,submission:k,error:ae}=Zm(!1,j,w),re=_.location,le=Wi(_.location,Z,w&&w.state);le={...le,...n.history.encodeLocation(le)};let se=w&&w.replace!=null?w.replace:void 0,fe="PUSH";se===!0?fe="REPLACE":se===!1||k!=null&&Ut(k.formMethod)&&k.formAction===_.location.pathname+_.location.search&&(fe="REPLACE");let ne=w&&"preventScrollReset"in w?w.preventScrollReset===!0:void 0,ie=(w&&w.flushSync)===!0,Re=yu({currentLocation:re,nextLocation:le,historyAction:fe});if(Re){ya(Re,{state:"blocked",location:le,proceed(){ya(Re,{state:"proceeding",proceed:void 0,reset:void 0,location:le}),ln(x,w)},reset(){let je=new Map(_.blockers);je.set(Re,Ki),ot({blockers:je})}});return}await ua(fe,le,{submission:k,pendingError:ae,preventScrollReset:ne,replace:w&&w.replace,enableViewTransition:w&&w.viewTransition,flushSync:ie})}function Jn(){he||(he=ab()),Fn(),ot({revalidation:"loading"});let x=he.promise;return _.navigation.state==="submitting"?x:_.navigation.state==="idle"?(ua(_.historyAction,_.location,{startUninterruptedRevalidation:!0}),x):(ua(ce||_.historyAction,_.navigation.location,{overrideNavigation:_.navigation,enableViewTransition:Ue===!0}),x)}async function ua(x,w,j){pe&&pe.abort(),pe=null,ce=x,ze=(j&&j.startUninterruptedRevalidation)===!0,Nl(_.location,_.matches),ge=(j&&j.preventScrollReset)===!0,Ue=(j&&j.enableViewTransition)===!0;let Z=y||d,k=j&&j.overrideNavigation,ae=j?.initialHydration&&_.matches&&_.matches.length>0&&!J?_.matches:El(Z,w,p),re=(j&&j.flushSync)===!0;if(ae&&_.initialized&&!Le&&kg(_.location,w)&&!(j&&j.submission&&Ut(j.submission.formMethod))){ia(w,{matches:ae},{flushSync:re});return}let le=_l(ae,Z,w.pathname);if(le.active&&le.matches&&(ae=le.matches),!ae){let{error:at,notFoundMatches:be,route:Ge}=un(w.pathname);ia(w,{matches:be,loaderData:{},errors:{[Ge.id]:at}},{flushSync:re});return}pe=new AbortController;let se=Xn(n.history,w,pe.signal,j&&j.submission),fe=n.unstable_getContext?await n.unstable_getContext():new Vm,ne;if(j&&j.pendingError)ne=[xl(ae).route.id,{type:"error",error:j.pendingError}];else if(j&&j.submission&&Ut(j.submission.formMethod)){let at=await no(se,w,j.submission,ae,fe,le.active,j&&j.initialHydration===!0,{replace:j.replace,flushSync:re});if(at.shortCircuited)return;if(at.pendingActionResult){let[be,Ge]=at.pendingActionResult;if(Kt(Ge)&&Ii(Ge.error)&&Ge.error.status===404){pe=null,ia(w,{matches:at.matches,loaderData:{},errors:{[be]:Ge.error}});return}}ae=at.matches||ae,ne=at.pendingActionResult,k=Es(w,j.submission),re=!1,le.active=!1,se=Xn(n.history,se.url,se.signal)}let{shortCircuited:ie,matches:Re,loaderData:je,errors:$e}=await io(se,w,ae,fe,le.active,k,j&&j.submission,j&&j.fetcherSubmission,j&&j.replace,j&&j.initialHydration===!0,re,ne);ie||(pe=null,ia(w,{matches:Re||ae,...ty(ne),loaderData:je,errors:$e}))}async function no(x,w,j,Z,k,ae,re,le={}){Fn();let se=Pg(w,j);if(ot({navigation:se},{flushSync:le.flushSync===!0}),ae){let ie=await zl(Z,w.pathname,x.signal);if(ie.type==="aborted")return{shortCircuited:!0};if(ie.type==="error"){if(ie.partialMatches.length===0){let{matches:je,route:$e}=Dr(d);return{matches:je,pendingActionResult:[$e.id,{type:"error",error:ie.error}]}}let Re=xl(ie.partialMatches).route.id;return{matches:ie.partialMatches,pendingActionResult:[Re,{type:"error",error:ie.error}]}}else if(ie.matches)Z=ie.matches;else{let{notFoundMatches:Re,error:je,route:$e}=un(w.pathname);return{matches:Re,pendingActionResult:[$e.id,{type:"error",error:je}]}}}let fe,ne=Cr(Z,w);if(!ne.route.action&&!ne.route.lazy)fe={type:"error",error:la(405,{method:x.method,pathname:w.pathname,routeId:ne.route.id})};else{let ie=Vn(s,f,x,Z,ne,re?[]:o,k),Re=await Fa(x,ie,k,null);if(fe=Re[ne.route.id],!fe){for(let je of Z)if(Re[je.route.id]){fe=Re[je.route.id];break}}if(x.signal.aborted)return{shortCircuited:!0}}if(Fl(fe)){let ie;return le&&le.replace!=null?ie=le.replace:ie=Wm(fe.response.headers.get("Location"),new URL(x.url),p)===_.location.pathname+_.location.search,await wa(x,fe,!0,{submission:j,replace:ie}),{shortCircuited:!0}}if(Kt(fe)){let ie=xl(Z,ne.route.id);return(le&&le.replace)!==!0&&(ce="PUSH"),{matches:Z,pendingActionResult:[ie.route.id,fe,ne.route.id]}}return{matches:Z,pendingActionResult:[ne.route.id,fe]}}async function io(x,w,j,Z,k,ae,re,le,se,fe,ne,ie){let Re=ae||Es(w,re),je=re||le||ly(Re),$e=!ze&&!fe;if(k){if($e){let ct=Ht(ie);ot({navigation:Re,...ct!==void 0?{actionData:ct}:{}},{flushSync:ne})}let Se=await zl(j,w.pathname,x.signal);if(Se.type==="aborted")return{shortCircuited:!0};if(Se.type==="error"){if(Se.partialMatches.length===0){let{matches:za,route:Mt}=Dr(d);return{matches:za,loaderData:{},errors:{[Mt.id]:Se.error}}}let ct=xl(Se.partialMatches).route.id;return{matches:Se.partialMatches,loaderData:{},errors:{[ct]:Se.error}}}else if(Se.matches)j=Se.matches;else{let{error:ct,notFoundMatches:za,route:Mt}=un(w.pathname);return{matches:za,loaderData:{},errors:{[Mt.id]:ct}}}}let at=y||d,{dsMatches:be,revalidatingFetchers:Ge}=Km(x,Z,s,f,n.history,_,j,je,w,fe?[]:o,fe===!0,Le,Be,P,X,E,at,p,n.patchRoutesOnNavigation!=null,ie);if(ee=++W,!n.dataStrategy&&!be.some(Se=>Se.shouldLoad)&&!be.some(Se=>Se.route.unstable_middleware)&&Ge.length===0){let Se=wl();return ia(w,{matches:j,loaderData:{},errors:ie&&Kt(ie[1])?{[ie[0]]:ie[1].error}:null,...ty(ie),...Se?{fetchers:new Map(_.fetchers)}:{}},{flushSync:ne}),{shortCircuited:!0}}if($e){let Se={};if(!k){Se.navigation=Re;let ct=Ht(ie);ct!==void 0&&(Se.actionData=ct)}Ge.length>0&&(Se.fetchers=uo(Ge)),ot(Se,{flushSync:ne})}Ge.forEach(Se=>{ma(Se.key),Se.controller&&C.set(Se.key,Se.controller)});let pa=()=>Ge.forEach(Se=>ma(Se.key));pe&&pe.signal.addEventListener("abort",pa);let{loaderResults:Bt,fetcherResults:At}=await hu(be,Ge,x,Z);if(x.signal.aborted)return{shortCircuited:!0};pe&&pe.signal.removeEventListener("abort",pa),Ge.forEach(Se=>C.delete(Se.key));let kt=Mr(Bt);if(kt)return await wa(x,kt.result,!0,{replace:se}),{shortCircuited:!0};if(kt=Mr(At),kt)return E.add(kt.key),await wa(x,kt.result,!0,{replace:se}),{shortCircuited:!0};let{loaderData:on,errors:$a}=Im(_,j,Bt,ie,Ge,At);fe&&_.errors&&($a={..._.errors,...$a});let Ma=wl(),Na=Dl(ee),_a=Ma||Na||Ge.length>0;return{matches:j,loaderData:on,errors:$a,..._a?{fetchers:new Map(_.fetchers)}:{}}}function Ht(x){if(x&&!Kt(x[1]))return{[x[0]]:x[1].data};if(_.actionData)return Object.keys(_.actionData).length===0?null:_.actionData}function uo(x){return x.forEach(w=>{let j=_.fetchers.get(w.key),Z=ki(void 0,j?j.data:void 0);_.fetchers.set(w.key,Z)}),new Map(_.fetchers)}async function fu(x,w,j,Z){ma(x);let k=(Z&&Z.flushSync)===!0,ae=y||d,re=As(_.location,_.matches,p,j,w,Z?.relative),le=El(ae,re,p),se=_l(le,ae,re);if(se.active&&se.matches&&(le=se.matches),!le){Ie(x,w,la(404,{pathname:re}),{flushSync:k});return}let{path:fe,submission:ne,error:ie}=Zm(!0,re,Z);if(ie){Ie(x,w,ie,{flushSync:k});return}let Re=n.unstable_getContext?await n.unstable_getContext():new Vm,je=(Z&&Z.preventScrollReset)===!0;if(ne&&Ut(ne.formMethod)){await du(x,w,fe,le,Re,se.active,k,je,ne);return}X.set(x,{routeId:w,path:fe}),await nn(x,w,fe,le,Re,se.active,k,je,ne)}async function du(x,w,j,Z,k,ae,re,le,se){Fn(),X.delete(x);let fe=_.fetchers.get(x);Dt(x,Ig(se,fe),{flushSync:re});let ne=new AbortController,ie=Xn(n.history,j,ne.signal,se);if(ae){let et=await zl(Z,new URL(ie.url).pathname,ie.signal,x);if(et.type==="aborted")return;if(et.type==="error"){Ie(x,w,et.error,{flushSync:re});return}else if(et.matches)Z=et.matches;else{Ie(x,w,la(404,{pathname:j}),{flushSync:re});return}}let Re=Cr(Z,j);if(!Re.route.action&&!Re.route.lazy){let et=la(405,{method:se.formMethod,pathname:j,routeId:w});Ie(x,w,et,{flushSync:re});return}C.set(x,ne);let je=W,$e=Vn(s,f,ie,Z,Re,o,k),be=(await Fa(ie,$e,k,x))[Re.route.id];if(ie.signal.aborted){C.get(x)===ne&&C.delete(x);return}if(P.has(x)){if(Fl(be)||Kt(be)){Dt(x,Sl(void 0));return}}else{if(Fl(be))if(C.delete(x),ee>je){Dt(x,Sl(void 0));return}else return E.add(x),Dt(x,ki(se)),wa(ie,be,!1,{fetcherSubmission:se,preventScrollReset:le});if(Kt(be)){Ie(x,w,be.error);return}}let Ge=_.navigation.location||_.location,pa=Xn(n.history,Ge,ne.signal),Bt=y||d,At=_.navigation.state!=="idle"?El(Bt,_.navigation.location,p):_.matches;Ne(At,"Didn't find any matches after fetcher action");let kt=++W;Te.set(x,kt);let on=ki(se,be.data);_.fetchers.set(x,on);let{dsMatches:$a,revalidatingFetchers:Ma}=Km(pa,k,s,f,n.history,_,At,se,Ge,o,!1,Le,Be,P,X,E,Bt,p,n.patchRoutesOnNavigation!=null,[Re.route.id,be]);Ma.filter(et=>et.key!==x).forEach(et=>{let Wa=et.key,gu=_.fetchers.get(Wa),bu=ki(void 0,gu?gu.data:void 0);_.fetchers.set(Wa,bu),ma(Wa),et.controller&&C.set(Wa,et.controller)}),ot({fetchers:new Map(_.fetchers)});let Na=()=>Ma.forEach(et=>ma(et.key));ne.signal.addEventListener("abort",Na);let{loaderResults:_a,fetcherResults:Se}=await hu($a,Ma,pa,k);if(ne.signal.aborted)return;if(ne.signal.removeEventListener("abort",Na),Te.delete(x),C.delete(x),Ma.forEach(et=>C.delete(et.key)),_.fetchers.has(x)){let et=Sl(be.data);_.fetchers.set(x,et)}let ct=Mr(_a);if(ct)return wa(pa,ct.result,!1,{preventScrollReset:le});if(ct=Mr(Se),ct)return E.add(ct.key),wa(pa,ct.result,!1,{preventScrollReset:le});let{loaderData:za,errors:Mt}=Im(_,At,_a,void 0,Ma,Se);Dl(kt),_.navigation.state==="loading"&&kt>ee?(Ne(ce,"Expected pending action"),pe&&pe.abort(),ia(_.navigation.location,{matches:At,loaderData:za,errors:Mt,fetchers:new Map(_.fetchers)})):(ot({errors:Mt,loaderData:ey(_.loaderData,za,At,Mt),fetchers:new Map(_.fetchers)}),Le=!1)}async function nn(x,w,j,Z,k,ae,re,le,se){let fe=_.fetchers.get(x);Dt(x,ki(se,fe?fe.data:void 0),{flushSync:re});let ne=new AbortController,ie=Xn(n.history,j,ne.signal);if(ae){let Ge=await zl(Z,new URL(ie.url).pathname,ie.signal,x);if(Ge.type==="aborted")return;if(Ge.type==="error"){Ie(x,w,Ge.error,{flushSync:re});return}else if(Ge.matches)Z=Ge.matches;else{Ie(x,w,la(404,{pathname:j}),{flushSync:re});return}}let Re=Cr(Z,j);C.set(x,ne);let je=W,$e=Vn(s,f,ie,Z,Re,o,k),be=(await Fa(ie,$e,k,x))[Re.route.id];if(C.get(x)===ne&&C.delete(x),!ie.signal.aborted){if(P.has(x)){Dt(x,Sl(void 0));return}if(Fl(be))if(ee>je){Dt(x,Sl(void 0));return}else{E.add(x),await wa(ie,be,!1,{preventScrollReset:le});return}if(Kt(be)){Ie(x,w,be.error);return}Dt(x,Sl(be.data))}}async function wa(x,w,j,{submission:Z,fetcherSubmission:k,preventScrollReset:ae,replace:re}={}){w.response.headers.has("X-Remix-Revalidate")&&(Le=!0);let le=w.response.headers.get("Location");Ne(le,"Expected a Location header on the redirect Response"),le=Wm(le,new URL(x.url),p);let se=Wi(_.location,le,{_isRedirect:!0});if(r){let $e=!1;if(w.response.headers.has("X-Remix-Reload-Document"))$e=!0;else if(Gs(le)){const at=Oy(le,!0);$e=at.origin!==i.location.origin||na(at.pathname,p)==null}if($e){re?i.location.replace(le):i.location.assign(le);return}}pe=null;let fe=re===!0||w.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:ne,formAction:ie,formEncType:Re}=_.navigation;!Z&&!k&&ne&&ie&&Re&&(Z=ly(_.navigation));let je=Z||k;if(Ng.has(w.response.status)&&je&&Ut(je.formMethod))await ua(fe,se,{submission:{...je,formAction:le},preventScrollReset:ae||ge,enableViewTransition:j?Ue:void 0});else{let $e=Es(se,Z);await ua(fe,se,{overrideNavigation:$e,fetcherSubmission:k,preventScrollReset:ae||ge,enableViewTransition:j?Ue:void 0})}}async function Fa(x,w,j,Z){let k,ae={};try{k=await Gg(m,x,w,Z,j,!1)}catch(re){return w.filter(le=>le.shouldLoad).forEach(le=>{ae[le.route.id]={type:"error",error:re}}),ae}if(x.signal.aborted)return ae;for(let[re,le]of Object.entries(k))if($g(le)){let se=le.result;ae[re]={type:"redirect",response:Zg(se,x,re,w,p)}}else ae[re]=await Qg(le);return ae}async function hu(x,w,j,Z){let k=Fa(j,x,Z,null),ae=Promise.all(w.map(async se=>{if(se.matches&&se.match&&se.request&&se.controller){let ne=(await Fa(se.request,se.matches,Z,se.key))[se.match.route.id];return{[se.key]:ne}}else return Promise.resolve({[se.key]:{type:"error",error:la(404,{pathname:se.path})}})})),re=await k,le=(await ae).reduce((se,fe)=>Object.assign(se,fe),{});return{loaderResults:re,fetcherResults:le}}function Fn(){Le=!0,X.forEach((x,w)=>{C.has(w)&&Be.add(w),ma(w)})}function Dt(x,w,j={}){_.fetchers.set(x,w),ot({fetchers:new Map(_.fetchers)},{flushSync:(j&&j.flushSync)===!0})}function Ie(x,w,j,Z={}){let k=xl(_.matches,w);gt(x),ot({errors:{[k.route.id]:j},fetchers:new Map(_.fetchers)},{flushSync:(Z&&Z.flushSync)===!0})}function ra(x){return I.set(x,(I.get(x)||0)+1),P.has(x)&&P.delete(x),_.fetchers.get(x)||_g}function gt(x){let w=_.fetchers.get(x);C.has(x)&&!(w&&w.state==="loading"&&Te.has(x))&&ma(x),X.delete(x),Te.delete(x),E.delete(x),P.delete(x),Be.delete(x),_.fetchers.delete(x)}function ro(x){let w=(I.get(x)||0)-1;w<=0?(I.delete(x),P.add(x)):I.set(x,w),ot({fetchers:new Map(_.fetchers)})}function ma(x){let w=C.get(x);w&&(w.abort(),C.delete(x))}function mu(x){for(let w of x){let j=ra(w),Z=Sl(j.data);_.fetchers.set(w,Z)}}function wl(){let x=[],w=!1;for(let j of E){let Z=_.fetchers.get(j);Ne(Z,`Expected fetcher: ${j}`),Z.state==="loading"&&(E.delete(j),x.push(j),w=!0)}return mu(x),w}function Dl(x){let w=[];for(let[j,Z]of Te)if(Z<x){let k=_.fetchers.get(j);Ne(k,`Expected fetcher: ${j}`),k.state==="loading"&&(ma(j),Te.delete(j),w.push(j))}return mu(w),w.length>0}function Da(x,w){let j=_.blockers.get(x)||Ki;return ue.get(x)!==w&&ue.set(x,w),j}function Ml(x){_.blockers.delete(x),ue.delete(x)}function ya(x,w){let j=_.blockers.get(x)||Ki;Ne(j.state==="unblocked"&&w.state==="blocked"||j.state==="blocked"&&w.state==="blocked"||j.state==="blocked"&&w.state==="proceeding"||j.state==="blocked"&&w.state==="unblocked"||j.state==="proceeding"&&w.state==="unblocked",`Invalid blocker state transition: ${j.state} -> ${w.state}`);let Z=new Map(_.blockers);Z.set(x,w),ot({blockers:Z})}function yu({currentLocation:x,nextLocation:w,historyAction:j}){if(ue.size===0)return;ue.size>1&&dt(!1,"A router only supports one blocker at a time");let Z=Array.from(ue.entries()),[k,ae]=Z[Z.length-1],re=_.blockers.get(k);if(!(re&&re.state==="proceeding")&&ae({currentLocation:x,nextLocation:w,historyAction:j}))return k}function un(x){let w=la(404,{pathname:x}),j=y||d,{matches:Z,route:k}=Dr(j);return{notFoundMatches:Z,route:k,error:w}}function pu(x,w,j){if(M=x,Q=w,U=j||null,!H&&_.navigation===Ss){H=!0;let Z=vu(_.location,_.matches);Z!=null&&ot({restoreScrollPosition:Z})}return()=>{M=null,Q=null,U=null}}function rn(x,w){return U&&U(x,w.map(Z=>rg(Z,_.loaderData)))||x.key}function Nl(x,w){if(M&&Q){let j=rn(x,w);M[j]=Q()}}function vu(x,w){if(M){let j=rn(x,w),Z=M[j];if(typeof Z=="number")return Z}return null}function _l(x,w,j){if(n.patchRoutesOnNavigation)if(x){if(Object.keys(x[0].params).length>0)return{active:!0,matches:zr(w,j,p,!0)}}else return{active:!0,matches:zr(w,j,p,!0)||[]};return{active:!1,matches:null}}async function zl(x,w,j,Z){if(!n.patchRoutesOnNavigation)return{type:"success",matches:x};let k=x;for(;;){let ae=y==null,re=y||d,le=f;try{await n.patchRoutesOnNavigation({signal:j,path:w,matches:k,fetcherKey:Z,patch:(ne,ie)=>{j.aborted||km(ne,ie,re,le,s,!1)}})}catch(ne){return{type:"error",error:ne,partialMatches:k}}finally{ae&&!j.aborted&&(d=[...d])}if(j.aborted)return{type:"aborted"};let se=El(re,w,p);if(se)return{type:"success",matches:se};let fe=zr(re,w,p,!0);if(!fe||k.length===fe.length&&k.every((ne,ie)=>ne.route.id===fe[ie].route.id))return{type:"success",matches:null};k=fe}}function $n(x){f={},y=Pi(x,s,void 0,f)}function Wn(x,w,j=!1){let Z=y==null;km(x,w,y||d,f,s,j),Z&&(d=[...d],ot({}))}return $={get basename(){return p},get future(){return g},get state(){return _},get routes(){return d},get window(){return i},initialize:Ot,subscribe:ha,enableScrollRestoration:pu,navigate:ln,fetch:fu,revalidate:Jn,createHref:x=>n.history.createHref(x),encodeLocation:x=>n.history.encodeLocation(x),getFetcher:ra,deleteFetcher:ro,dispose:ke,getBlocker:Da,deleteBlocker:Ml,patchRoutes:Wn,_internalFetchControllers:C,_internalSetRoutes:$n,_internalSetStateDoNotUseOrYouWillBreakYourApp(x){ot(x)}},$}function Lg(n){return n!=null&&("formData"in n&&n.formData!=null||"body"in n&&n.body!==void 0)}function As(n,i,r,o,s,f){let d,y;if(s){d=[];for(let m of i)if(d.push(m),m.route.id===s){y=m;break}}else d=i,y=i[i.length-1];let p=Ys(o||".",qs(d),na(n.pathname,r)||n.pathname,f==="path");if(o==null&&(p.search=n.search,p.hash=n.hash),(o==null||o===""||o===".")&&y){let m=Xs(p.search);if(y.route.index&&!m)p.search=p.search?p.search.replace(/^\?/,"?index&"):"?index";else if(!y.route.index&&m){let g=new URLSearchParams(p.search),b=g.getAll("index");g.delete("index"),b.filter(M=>M).forEach(M=>g.append("index",M));let T=g.toString();p.search=T?`?${T}`:""}}return r!=="/"&&(p.pathname=Sg({basename:r,pathname:p.pathname})),Tl(p)}function Zm(n,i,r){if(!r||!Lg(r))return{path:i};if(r.formMethod&&!Wg(r.formMethod))return{path:i,error:la(405,{method:r.formMethod})};let o=()=>({path:i,error:la(400,{type:"invalid-body"})}),f=(r.formMethod||"get").toUpperCase(),d=Hy(i);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!Ut(f))return o();let b=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((T,[M,U])=>`${T}${M}=${U}
`,""):String(r.body);return{path:i,submission:{formMethod:f,formAction:d,formEncType:r.formEncType,formData:void 0,json:void 0,text:b}}}else if(r.formEncType==="application/json"){if(!Ut(f))return o();try{let b=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:i,submission:{formMethod:f,formAction:d,formEncType:r.formEncType,formData:void 0,json:b,text:void 0}}}catch{return o()}}}Ne(typeof FormData=="function","FormData is not available in this environment");let y,p;if(r.formData)y=Ms(r.formData),p=r.formData;else if(r.body instanceof FormData)y=Ms(r.body),p=r.body;else if(r.body instanceof URLSearchParams)y=r.body,p=Pm(y);else if(r.body==null)y=new URLSearchParams,p=new FormData;else try{y=new URLSearchParams(r.body),p=Pm(y)}catch{return o()}let m={formMethod:f,formAction:d,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:p,json:void 0,text:void 0};if(Ut(m.formMethod))return{path:i,submission:m};let g=Ol(i);return n&&g.search&&Xs(g.search)&&y.append("index",""),g.search=`?${y}`,{path:Tl(g),submission:m}}function Km(n,i,r,o,s,f,d,y,p,m,g,b,T,M,U,Q,H,K,J,F){let ye=F?Kt(F[1])?F[1].error:F[1].data:void 0,$=s.createURL(f.location),_=s.createURL(p),ce;if(g&&f.errors){let ze=Object.keys(f.errors)[0];ce=d.findIndex(Le=>Le.route.id===ze)}else if(F&&Kt(F[1])){let ze=F[0];ce=d.findIndex(Le=>Le.route.id===ze)-1}let ge=F?F[1].statusCode:void 0,pe=ge&&ge>=400,Ue={currentUrl:$,currentParams:f.matches[0]?.params||{},nextUrl:_,nextParams:d[0].params,...y,actionResult:ye,actionStatus:ge},Pe=d.map((ze,Le)=>{let{route:Be}=ze,C=null;if(ce!=null&&Le>ce?C=!1:Be.lazy?C=!0:Be.loader==null?C=!1:g?C=ws(Be,f.loaderData,f.errors):jg(f.loaderData,f.matches[Le],ze)&&(C=!0),C!==null)return Ds(r,o,n,ze,m,i,C);let W=pe?!1:b||$.pathname+$.search===_.pathname+_.search||$.search!==_.search||Hg(f.matches[Le],ze),ee={...Ue,defaultShouldRevalidate:W},Te=Xr(ze,ee);return Ds(r,o,n,ze,m,i,Te,ee)}),Fe=[];return U.forEach((ze,Le)=>{if(g||!d.some(I=>I.route.id===ze.routeId)||M.has(Le))return;let Be=f.fetchers.get(Le),C=Be&&Be.state!=="idle"&&Be.data===void 0,W=El(H,ze.path,K);if(!W){if(J&&C)return;Fe.push({key:Le,routeId:ze.routeId,path:ze.path,matches:null,match:null,request:null,controller:null});return}if(Q.has(Le))return;let ee=Cr(W,ze.path),Te=new AbortController,E=Xn(s,ze.path,Te.signal),X=null;if(T.has(Le))T.delete(Le),X=Vn(r,o,E,W,ee,m,i);else if(C)b&&(X=Vn(r,o,E,W,ee,m,i));else{let I={...Ue,defaultShouldRevalidate:pe?!1:b};Xr(ee,I)&&(X=Vn(r,o,E,W,ee,m,i,I))}X&&Fe.push({key:Le,routeId:ze.routeId,path:ze.path,matches:X,match:ee,request:E,controller:Te})}),{dsMatches:Pe,revalidatingFetchers:Fe}}function ws(n,i,r){if(n.lazy)return!0;if(!n.loader)return!1;let o=i!=null&&n.id in i,s=r!=null&&r[n.id]!==void 0;return!o&&s?!1:typeof n.loader=="function"&&n.loader.hydrate===!0?!0:!o&&!s}function jg(n,i,r){let o=!i||r.route.id!==i.route.id,s=!n.hasOwnProperty(r.route.id);return o||s}function Hg(n,i){let r=n.route.path;return n.pathname!==i.pathname||r!=null&&r.endsWith("*")&&n.params["*"]!==i.params["*"]}function Xr(n,i){if(n.route.shouldRevalidate){let r=n.route.shouldRevalidate(i);if(typeof r=="boolean")return r}return i.defaultShouldRevalidate}function km(n,i,r,o,s,f){let d;if(n){let m=o[n];Ne(m,`No route found to patch children into: routeId = ${n}`),m.children||(m.children=[]),d=m.children}else d=r;let y=[],p=[];if(i.forEach(m=>{let g=d.find(b=>zy(m,b));g?p.push({existingRoute:g,newRoute:m}):y.push(m)}),y.length>0){let m=Pi(y,s,[n||"_","patch",String(d?.length||"0")],o);d.push(...m)}if(f&&p.length>0)for(let m=0;m<p.length;m++){let{existingRoute:g,newRoute:b}=p[m],T=g,[M]=Pi([b],s,[],{},!0);Object.assign(T,{element:M.element?M.element:T.element,errorElement:M.errorElement?M.errorElement:T.errorElement,hydrateFallbackElement:M.hydrateFallbackElement?M.hydrateFallbackElement:T.hydrateFallbackElement})}}function zy(n,i){return"id"in n&&"id"in i&&n.id===i.id?!0:n.index===i.index&&n.path===i.path&&n.caseSensitive===i.caseSensitive?(!n.children||n.children.length===0)&&(!i.children||i.children.length===0)?!0:n.children.every((r,o)=>i.children?.some(s=>zy(r,s))):!1}var Jm=new WeakMap,Cy=({key:n,route:i,manifest:r,mapRouteProperties:o})=>{let s=r[i.id];if(Ne(s,"No route found in manifest"),!s.lazy||typeof s.lazy!="object")return;let f=s.lazy[n];if(!f)return;let d=Jm.get(s);d||(d={},Jm.set(s,d));let y=d[n];if(y)return y;let p=(async()=>{let m=lg(n),b=s[n]!==void 0&&n!=="hasErrorBoundary";if(m)dt(!m,"Route property "+n+" is not a supported lazy route property. This property will be ignored."),d[n]=Promise.resolve();else if(b)dt(!1,`Route "${s.id}" has a static property "${n}" defined. The lazy property will be ignored.`);else{let T=await f();T!=null&&(Object.assign(s,{[n]:T}),Object.assign(s,o(s)))}typeof s.lazy=="object"&&(s.lazy[n]=void 0,Object.values(s.lazy).every(T=>T===void 0)&&(s.lazy=void 0))})();return d[n]=p,p},Fm=new WeakMap;function Bg(n,i,r,o,s){let f=r[n.id];if(Ne(f,"No route found in manifest"),!n.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof n.lazy=="function"){let g=Fm.get(f);if(g)return{lazyRoutePromise:g,lazyHandlerPromise:g};let b=(async()=>{Ne(typeof n.lazy=="function","No lazy route function found");let T=await n.lazy(),M={};for(let U in T){let Q=T[U];if(Q===void 0)continue;let H=ig(U),J=f[U]!==void 0&&U!=="hasErrorBoundary";H?dt(!H,"Route property "+U+" is not a supported property to be returned from a lazy route function. This property will be ignored."):J?dt(!J,`Route "${f.id}" has a static property "${U}" defined but its lazy function is also returning a value for this property. The lazy route property "${U}" will be ignored.`):M[U]=Q}Object.assign(f,M),Object.assign(f,{...o(f),lazy:void 0})})();return Fm.set(f,b),b.catch(()=>{}),{lazyRoutePromise:b,lazyHandlerPromise:b}}let d=Object.keys(n.lazy),y=[],p;for(let g of d){if(s&&s.includes(g))continue;let b=Cy({key:g,route:n,manifest:r,mapRouteProperties:o});b&&(y.push(b),g===i&&(p=b))}let m=y.length>0?Promise.all(y).then(()=>{}):void 0;return m?.catch(()=>{}),p?.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:p}}async function $m(n){let i=n.matches.filter(s=>s.shouldLoad),r={};return(await Promise.all(i.map(s=>s.resolve()))).forEach((s,f)=>{r[i[f].route.id]=s}),r}async function qg(n){return n.matches.some(i=>i.route.unstable_middleware)?Uy(n,()=>$m(n)):$m(n)}function Uy(n,i){return Yg(n,i,o=>o,Jg,r);function r(o,s,f){if(f)return Promise.resolve(Object.assign(f.value,{[s]:{type:"error",result:o}}));{let{matches:d}=n,y=Math.min(d.findIndex(m=>m.route.id===s)||0,d.findIndex(m=>m.unstable_shouldCallHandler())||0),p=xl(d,d[y].route.id).route.id;return Promise.resolve({[p]:{type:"error",result:o}})}}}async function Yg(n,i,r,o,s){let{matches:f,request:d,params:y,context:p}=n,m=f.flatMap(b=>b.route.unstable_middleware?b.route.unstable_middleware.map(T=>[b.route.id,T]):[]);return await Ly({request:d,params:y,context:p},m,i,r,o,s)}async function Ly(n,i,r,o,s,f,d=0){let{request:y}=n;if(y.signal.aborted)throw y.signal.reason??new Error(`Request aborted: ${y.method} ${y.url}`);let p=i[d];if(!p)return await r();let[m,g]=p,b,T=async()=>{if(b)throw new Error("You may only call `next()` once per middleware");try{return b={value:await Ly(n,i,r,o,s,f,d+1)},b.value}catch(M){return b={value:await f(M,m,b)},b.value}};try{let M=await g(n,T),U=M!=null?o(M):void 0;return s(U)?U:b?U??b.value:(b={value:await T()},b.value)}catch(M){return await f(M,m,b)}}function jy(n,i,r,o,s){let f=Cy({key:"unstable_middleware",route:o.route,manifest:i,mapRouteProperties:n}),d=Bg(o.route,Ut(r.method)?"action":"loader",i,n,s);return{middleware:f,route:d.lazyRoutePromise,handler:d.lazyHandlerPromise}}function Ds(n,i,r,o,s,f,d,y=null){let p=!1,m=jy(n,i,r,o,s);return{...o,_lazyPromises:m,shouldLoad:d,unstable_shouldRevalidateArgs:y,unstable_shouldCallHandler(g){return p=!0,y?typeof g=="boolean"?Xr(o,{...y,defaultShouldRevalidate:g}):Xr(o,y):d},resolve(g){return p||d||g&&!Ut(r.method)&&(o.route.lazy||o.route.loader)?Xg({request:r,match:o,lazyHandlerPromise:m?.handler,lazyRoutePromise:m?.route,handlerOverride:g,scopedContext:f}):Promise.resolve({type:"data",result:void 0})}}}function Vn(n,i,r,o,s,f,d,y=null){return o.map(p=>p.route.id!==s.route.id?{...p,shouldLoad:!1,unstable_shouldRevalidateArgs:y,unstable_shouldCallHandler:()=>!1,_lazyPromises:jy(n,i,r,p,f),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Ds(n,i,r,p,f,d,!0,y))}async function Gg(n,i,r,o,s,f){r.some(m=>m._lazyPromises?.middleware)&&await Promise.all(r.map(m=>m._lazyPromises?.middleware));let d={request:i,params:r[0].params,context:s,matches:r},p=await n({...d,fetcherKey:o,unstable_runClientMiddleware:m=>{let g=d;return Uy(g,()=>m({...g,fetcherKey:o,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}))}});try{await Promise.all(r.flatMap(m=>[m._lazyPromises?.handler,m._lazyPromises?.route]))}catch{}return p}async function Xg({request:n,match:i,lazyHandlerPromise:r,lazyRoutePromise:o,handlerOverride:s,scopedContext:f}){let d,y,p=Ut(n.method),m=p?"action":"loader",g=b=>{let T,M=new Promise((H,K)=>T=K);y=()=>T(),n.signal.addEventListener("abort",y);let U=H=>typeof b!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${m}" [routeId: ${i.route.id}]`)):b({request:n,params:i.params,context:f},...H!==void 0?[H]:[]),Q=(async()=>{try{return{type:"data",result:await(s?s(K=>U(K)):U())}}catch(H){return{type:"error",result:H}}})();return Promise.race([Q,M])};try{let b=p?i.route.action:i.route.loader;if(r||o)if(b){let T,[M]=await Promise.all([g(b).catch(U=>{T=U}),r,o]);if(T!==void 0)throw T;d=M}else{await r;let T=p?i.route.action:i.route.loader;if(T)[d]=await Promise.all([g(T),o]);else if(m==="action"){let M=new URL(n.url),U=M.pathname+M.search;throw la(405,{method:n.method,pathname:U,routeId:i.route.id})}else return{type:"data",result:void 0}}else if(b)d=await g(b);else{let T=new URL(n.url),M=T.pathname+T.search;throw la(404,{pathname:M})}}catch(b){return{type:"error",result:b}}finally{y&&n.signal.removeEventListener("abort",y)}return d}async function Vg(n){let i=n.headers.get("Content-Type");return i&&/\bapplication\/json\b/.test(i)?n.body==null?null:n.json():n.text()}async function Qg(n){let{result:i,type:r}=n;if(By(i)){let o;try{o=await Vg(i)}catch(s){return{type:"error",error:s}}return r==="error"?{type:"error",error:new Gr(i.status,i.statusText,o),statusCode:i.status,headers:i.headers}:{type:"data",data:o,statusCode:i.status,headers:i.headers}}return r==="error"?ay(i)?i.data instanceof Error?{type:"error",error:i.data,statusCode:i.init?.status,headers:i.init?.headers?new Headers(i.init.headers):void 0}:{type:"error",error:new Gr(i.init?.status||500,void 0,i.data),statusCode:Ii(i)?i.status:void 0,headers:i.init?.headers?new Headers(i.init.headers):void 0}:{type:"error",error:i,statusCode:Ii(i)?i.status:void 0}:ay(i)?{type:"data",data:i.data,statusCode:i.init?.status,headers:i.init?.headers?new Headers(i.init.headers):void 0}:{type:"data",data:i}}function Zg(n,i,r,o,s){let f=n.headers.get("Location");if(Ne(f,"Redirects returned/thrown from loaders/actions must have a Location header"),!Gs(f)){let d=o.slice(0,o.findIndex(y=>y.route.id===r)+1);f=As(new URL(i.url),d,s,f),n.headers.set("Location",f)}return n}function Wm(n,i,r){if(Gs(n)){let o=n,s=o.startsWith("//")?new URL(i.protocol+o):new URL(o),f=na(s.pathname,r)!=null;if(s.origin===i.origin&&f)return s.pathname+s.search+s.hash}return n}function Xn(n,i,r,o){let s=n.createURL(Hy(i)).toString(),f={signal:r};if(o&&Ut(o.formMethod)){let{formMethod:d,formEncType:y}=o;f.method=d.toUpperCase(),y==="application/json"?(f.headers=new Headers({"Content-Type":y}),f.body=JSON.stringify(o.json)):y==="text/plain"?f.body=o.text:y==="application/x-www-form-urlencoded"&&o.formData?f.body=Ms(o.formData):f.body=o.formData}return new Request(s,f)}function Ms(n){let i=new URLSearchParams;for(let[r,o]of n.entries())i.append(r,typeof o=="string"?o:o.name);return i}function Pm(n){let i=new FormData;for(let[r,o]of n.entries())i.append(r,o);return i}function Kg(n,i,r,o=!1,s=!1){let f={},d=null,y,p=!1,m={},g=r&&Kt(r[1])?r[1].error:void 0;return n.forEach(b=>{if(!(b.route.id in i))return;let T=b.route.id,M=i[T];if(Ne(!Fl(M),"Cannot handle redirect results in processLoaderData"),Kt(M)){let U=M.error;if(g!==void 0&&(U=g,g=void 0),d=d||{},s)d[T]=U;else{let Q=xl(n,T);d[Q.route.id]==null&&(d[Q.route.id]=U)}o||(f[T]=_y),p||(p=!0,y=Ii(M.error)?M.error.status:500),M.headers&&(m[T]=M.headers)}else f[T]=M.data,M.statusCode&&M.statusCode!==200&&!p&&(y=M.statusCode),M.headers&&(m[T]=M.headers)}),g!==void 0&&r&&(d={[r[0]]:g},r[2]&&(f[r[2]]=void 0)),{loaderData:f,errors:d,statusCode:y||200,loaderHeaders:m}}function Im(n,i,r,o,s,f){let{loaderData:d,errors:y}=Kg(i,r,o);return s.filter(p=>!p.matches||p.matches.some(m=>m.shouldLoad)).forEach(p=>{let{key:m,match:g,controller:b}=p;if(b&&b.signal.aborted)return;let T=f[m];if(Ne(T,"Did not find corresponding fetcher result"),Kt(T)){let M=xl(n.matches,g?.route.id);y&&y[M.route.id]||(y={...y,[M.route.id]:T.error}),n.fetchers.delete(m)}else if(Fl(T))Ne(!1,"Unhandled fetcher revalidation redirect");else{let M=Sl(T.data);n.fetchers.set(m,M)}}),{loaderData:d,errors:y}}function ey(n,i,r,o){let s=Object.entries(i).filter(([,f])=>f!==_y).reduce((f,[d,y])=>(f[d]=y,f),{});for(let f of r){let d=f.route.id;if(!i.hasOwnProperty(d)&&n.hasOwnProperty(d)&&f.route.loader&&(s[d]=n[d]),o&&o.hasOwnProperty(d))break}return s}function ty(n){return n?Kt(n[1])?{actionData:{}}:{actionData:{[n[0]]:n[1].data}}:{}}function xl(n,i){return(i?n.slice(0,n.findIndex(o=>o.route.id===i)+1):[...n]).reverse().find(o=>o.route.hasErrorBoundary===!0)||n[0]}function Dr(n){let i=n.length===1?n[0]:n.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:i}],route:i}}function la(n,{pathname:i,routeId:r,method:o,type:s,message:f}={}){let d="Unknown Server Error",y="Unknown @remix-run/router error";return n===400?(d="Bad Request",o&&i&&r?y=`You made a ${o} request to "${i}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:s==="invalid-body"&&(y="Unable to encode submission body")):n===403?(d="Forbidden",y=`Route "${r}" does not match URL "${i}"`):n===404?(d="Not Found",y=`No route matches URL "${i}"`):n===405&&(d="Method Not Allowed",o&&i&&r?y=`You made a ${o.toUpperCase()} request to "${i}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:o&&(y=`Invalid request method "${o.toUpperCase()}"`)),new Gr(n||500,d,new Error(y),!0)}function Mr(n){let i=Object.entries(n);for(let r=i.length-1;r>=0;r--){let[o,s]=i[r];if(Fl(s))return{key:o,result:s}}}function Hy(n){let i=typeof n=="string"?Ol(n):n;return Tl({...i,hash:""})}function kg(n,i){return n.pathname!==i.pathname||n.search!==i.search?!1:n.hash===""?i.hash!=="":n.hash===i.hash?!0:i.hash!==""}function Jg(n){return n!=null&&typeof n=="object"&&Object.entries(n).every(([i,r])=>typeof i=="string"&&Fg(r))}function Fg(n){return n!=null&&typeof n=="object"&&"type"in n&&"result"in n&&(n.type==="data"||n.type==="error")}function $g(n){return By(n.result)&&Mg.has(n.result.status)}function Kt(n){return n.type==="error"}function Fl(n){return(n&&n.type)==="redirect"}function ay(n){return typeof n=="object"&&n!=null&&"type"in n&&"data"in n&&"init"in n&&n.type==="DataWithResponseInit"}function By(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.headers=="object"&&typeof n.body<"u"}function Wg(n){return Dg.has(n.toUpperCase())}function Ut(n){return Ag.has(n.toUpperCase())}function Xs(n){return new URLSearchParams(n).getAll("index").some(i=>i==="")}function Cr(n,i){let r=typeof i=="string"?Ol(i).search:i.search;if(n[n.length-1].route.index&&Xs(r||""))return n[n.length-1];let o=Dy(n);return o[o.length-1]}function ly(n){let{formMethod:i,formAction:r,formEncType:o,text:s,formData:f,json:d}=n;if(!(!i||!r||!o)){if(s!=null)return{formMethod:i,formAction:r,formEncType:o,formData:void 0,json:void 0,text:s};if(f!=null)return{formMethod:i,formAction:r,formEncType:o,formData:f,json:void 0,text:void 0};if(d!==void 0)return{formMethod:i,formAction:r,formEncType:o,formData:void 0,json:d,text:void 0}}}function Es(n,i){return i?{state:"loading",location:n,formMethod:i.formMethod,formAction:i.formAction,formEncType:i.formEncType,formData:i.formData,json:i.json,text:i.text}:{state:"loading",location:n,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Pg(n,i){return{state:"submitting",location:n,formMethod:i.formMethod,formAction:i.formAction,formEncType:i.formEncType,formData:i.formData,json:i.json,text:i.text}}function ki(n,i){return n?{state:"loading",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:i}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:i}}function Ig(n,i){return{state:"submitting",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:i?i.data:void 0}}function Sl(n){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:n}}function eb(n,i){try{let r=n.sessionStorage.getItem(Ny);if(r){let o=JSON.parse(r);for(let[s,f]of Object.entries(o||{}))f&&Array.isArray(f)&&i.set(s,new Set(f||[]))}}catch{}}function tb(n,i){if(i.size>0){let r={};for(let[o,s]of i)r[o]=[...s];try{n.sessionStorage.setItem(Ny,JSON.stringify(r))}catch(o){dt(!1,`Failed to save applied view transitions in sessionStorage (${o}).`)}}}function ab(){let n,i,r=new Promise((o,s)=>{n=async f=>{o(f);try{await r}catch{}},i=async f=>{s(f);try{await r}catch{}}});return{promise:r,resolve:n,reject:i}}var en=O.createContext(null);en.displayName="DataRouter";var tu=O.createContext(null);tu.displayName="DataRouterState";O.createContext(!1);var Vs=O.createContext({isTransitioning:!1});Vs.displayName="ViewTransition";var qy=O.createContext(new Map);qy.displayName="Fetchers";var lb=O.createContext(null);lb.displayName="Await";var Oa=O.createContext(null);Oa.displayName="Navigation";var Jr=O.createContext(null);Jr.displayName="Location";var Aa=O.createContext({outlet:null,matches:[],isDataRoute:!1});Aa.displayName="Route";var Qs=O.createContext(null);Qs.displayName="RouteError";function nb(n,{relative:i}={}){Ne(au(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=O.useContext(Oa),{hash:s,pathname:f,search:d}=lu(n,{relative:i}),y=f;return r!=="/"&&(y=f==="/"?r:Ta([r,f])),o.createHref({pathname:y,search:d,hash:s})}function au(){return O.useContext(Jr)!=null}function tn(){return Ne(au(),"useLocation() may be used only in the context of a <Router> component."),O.useContext(Jr).location}var Yy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Gy(n){O.useContext(Oa).static||O.useLayoutEffect(n)}function Zn(){let{isDataRoute:n}=O.useContext(Aa);return n?gb():ib()}function ib(){Ne(au(),"useNavigate() may be used only in the context of a <Router> component.");let n=O.useContext(en),{basename:i,navigator:r}=O.useContext(Oa),{matches:o}=O.useContext(Aa),{pathname:s}=tn(),f=JSON.stringify(qs(o)),d=O.useRef(!1);return Gy(()=>{d.current=!0}),O.useCallback((p,m={})=>{if(dt(d.current,Yy),!d.current)return;if(typeof p=="number"){r.go(p);return}let g=Ys(p,JSON.parse(f),s,m.relative==="path");n==null&&i!=="/"&&(g.pathname=g.pathname==="/"?i:Ta([i,g.pathname])),(m.replace?r.replace:r.push)(g,m.state,m)},[i,r,f,s,n])}O.createContext(null);function ub(){let{matches:n}=O.useContext(Aa),i=n[n.length-1];return i?i.params:{}}function lu(n,{relative:i}={}){let{matches:r}=O.useContext(Aa),{pathname:o}=tn(),s=JSON.stringify(qs(r));return O.useMemo(()=>Ys(n,JSON.parse(s),o,i==="path"),[n,s,o,i])}function rb(n,i,r,o,s){Ne(au(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:f}=O.useContext(Oa),{matches:d}=O.useContext(Aa),y=d[d.length-1],p=y?y.params:{},m=y?y.pathname:"/",g=y?y.pathnameBase:"/",b=y&&y.route;{let J=b&&b.path||"";Xy(m,!b||J.endsWith("*")||J.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${J}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${J}"> to <Route path="${J==="/"?"*":`${J}/*`}">.`)}let T=tn(),M;M=T;let U=M.pathname||"/",Q=U;if(g!=="/"){let J=g.replace(/^\//,"").split("/");Q="/"+U.replace(/^\//,"").split("/").slice(J.length).join("/")}let H=El(n,{pathname:Q});return dt(b||H!=null,`No routes matched location "${M.pathname}${M.search}${M.hash}" `),dt(H==null||H[H.length-1].route.element!==void 0||H[H.length-1].route.Component!==void 0||H[H.length-1].route.lazy!==void 0,`Matched leaf route at location "${M.pathname}${M.search}${M.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),db(H&&H.map(J=>Object.assign({},J,{params:Object.assign({},p,J.params),pathname:Ta([g,f.encodeLocation?f.encodeLocation(J.pathname).pathname:J.pathname]),pathnameBase:J.pathnameBase==="/"?g:Ta([g,f.encodeLocation?f.encodeLocation(J.pathnameBase).pathname:J.pathnameBase])})),d,r,o,s)}function ob(){let n=vb(),i=Ii(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),r=n instanceof Error?n.stack:null,o="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},d=null;return console.error("Error handled by React Router default ErrorBoundary:",n),d=O.createElement(O.Fragment,null,O.createElement("p",null,"💿 Hey developer 👋"),O.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",O.createElement("code",{style:f},"ErrorBoundary")," or"," ",O.createElement("code",{style:f},"errorElement")," prop on your route.")),O.createElement(O.Fragment,null,O.createElement("h2",null,"Unexpected Application Error!"),O.createElement("h3",{style:{fontStyle:"italic"}},i),r?O.createElement("pre",{style:s},r):null,d)}var cb=O.createElement(ob,null),sb=class extends O.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,i){return i.location!==n.location||i.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:i.error,location:i.location,revalidation:n.revalidation||i.revalidation}}componentDidCatch(n,i){this.props.unstable_onError?this.props.unstable_onError(n,i):console.error("React Router caught the following error during render",n)}render(){return this.state.error!==void 0?O.createElement(Aa.Provider,{value:this.props.routeContext},O.createElement(Qs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function fb({routeContext:n,match:i,children:r}){let o=O.useContext(en);return o&&o.static&&o.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=i.route.id),O.createElement(Aa.Provider,{value:n},r)}function db(n,i=[],r=null,o=null,s=null){if(n==null){if(!r)return null;if(r.errors)n=r.matches;else if(i.length===0&&!r.initialized&&r.matches.length>0)n=r.matches;else return null}let f=n,d=r?.errors;if(d!=null){let m=f.findIndex(g=>g.route.id&&d?.[g.route.id]!==void 0);Ne(m>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(d).join(",")}`),f=f.slice(0,Math.min(f.length,m+1))}let y=!1,p=-1;if(r)for(let m=0;m<f.length;m++){let g=f[m];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(p=m),g.route.id){let{loaderData:b,errors:T}=r,M=g.route.loader&&!b.hasOwnProperty(g.route.id)&&(!T||T[g.route.id]===void 0);if(g.route.lazy||M){y=!0,p>=0?f=f.slice(0,p+1):f=[f[0]];break}}}return f.reduceRight((m,g,b)=>{let T,M=!1,U=null,Q=null;r&&(T=d&&g.route.id?d[g.route.id]:void 0,U=g.route.errorElement||cb,y&&(p<0&&b===0?(Xy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),M=!0,Q=null):p===b&&(M=!0,Q=g.route.hydrateFallbackElement||null)));let H=i.concat(f.slice(0,b+1)),K=()=>{let J;return T?J=U:M?J=Q:g.route.Component?J=O.createElement(g.route.Component,null):g.route.element?J=g.route.element:J=m,O.createElement(fb,{match:g,routeContext:{outlet:m,matches:H,isDataRoute:r!=null},children:J})};return r&&(g.route.ErrorBoundary||g.route.errorElement||b===0)?O.createElement(sb,{location:r.location,revalidation:r.revalidation,component:U,error:T,children:K(),routeContext:{outlet:null,matches:H,isDataRoute:!0},unstable_onError:o}):K()},null)}function Zs(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function hb(n){let i=O.useContext(en);return Ne(i,Zs(n)),i}function mb(n){let i=O.useContext(tu);return Ne(i,Zs(n)),i}function yb(n){let i=O.useContext(Aa);return Ne(i,Zs(n)),i}function Ks(n){let i=yb(n),r=i.matches[i.matches.length-1];return Ne(r.route.id,`${n} can only be used on routes that contain a unique "id"`),r.route.id}function pb(){return Ks("useRouteId")}function vb(){let n=O.useContext(Qs),i=mb("useRouteError"),r=Ks("useRouteError");return n!==void 0?n:i.errors?.[r]}function gb(){let{router:n}=hb("useNavigate"),i=Ks("useNavigate"),r=O.useRef(!1);return Gy(()=>{r.current=!0}),O.useCallback(async(s,f={})=>{dt(r.current,Yy),r.current&&(typeof s=="number"?n.navigate(s):await n.navigate(s,{fromRouteId:i,...f}))},[n,i])}var ny={};function Xy(n,i,r){!i&&!ny[n]&&(ny[n]=!0,dt(!1,r))}var iy={};function uy(n,i){!n&&!iy[i]&&(iy[i]=!0,console.warn(i))}function bb(n){let i={hasErrorBoundary:n.hasErrorBoundary||n.ErrorBoundary!=null||n.errorElement!=null};return n.Component&&(n.element&&dt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(i,{element:O.createElement(n.Component),Component:void 0})),n.HydrateFallback&&(n.hydrateFallbackElement&&dt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(i,{hydrateFallbackElement:O.createElement(n.HydrateFallback),HydrateFallback:void 0})),n.ErrorBoundary&&(n.errorElement&&dt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(i,{errorElement:O.createElement(n.ErrorBoundary),ErrorBoundary:void 0})),i}var Sb=["HydrateFallback","hydrateFallbackElement"],Eb=class{constructor(){this.status="pending",this.promise=new Promise((n,i)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",n(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",i(r))}})}};function xb({router:n,flushSync:i,unstable_onError:r}){let[o,s]=O.useState(n.state),[f,d]=O.useState(),[y,p]=O.useState({isTransitioning:!1}),[m,g]=O.useState(),[b,T]=O.useState(),[M,U]=O.useState(),Q=O.useRef(new Map),H=O.useCallback($=>{s(_=>($.errors&&r&&Object.entries($.errors).forEach(([ce,ge])=>{_.errors?.[ce]!==ge&&r(ge)}),$))},[r]),K=O.useCallback(($,{deletedFetchers:_,flushSync:ce,viewTransitionOpts:ge})=>{$.fetchers.forEach((Ue,Pe)=>{Ue.data!==void 0&&Q.current.set(Pe,Ue.data)}),_.forEach(Ue=>Q.current.delete(Ue)),uy(ce===!1||i!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let pe=n.window!=null&&n.window.document!=null&&typeof n.window.document.startViewTransition=="function";if(uy(ge==null||pe,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!ge||!pe){i&&ce?i(()=>H($)):O.startTransition(()=>H($));return}if(i&&ce){i(()=>{b&&(m&&m.resolve(),b.skipTransition()),p({isTransitioning:!0,flushSync:!0,currentLocation:ge.currentLocation,nextLocation:ge.nextLocation})});let Ue=n.window.document.startViewTransition(()=>{i(()=>H($))});Ue.finished.finally(()=>{i(()=>{g(void 0),T(void 0),d(void 0),p({isTransitioning:!1})})}),i(()=>T(Ue));return}b?(m&&m.resolve(),b.skipTransition(),U({state:$,currentLocation:ge.currentLocation,nextLocation:ge.nextLocation})):(d($),p({isTransitioning:!0,flushSync:!1,currentLocation:ge.currentLocation,nextLocation:ge.nextLocation}))},[n.window,i,b,m,H]);O.useLayoutEffect(()=>n.subscribe(K),[n,K]),O.useEffect(()=>{y.isTransitioning&&!y.flushSync&&g(new Eb)},[y]),O.useEffect(()=>{if(m&&f&&n.window){let $=f,_=m.promise,ce=n.window.document.startViewTransition(async()=>{O.startTransition(()=>H($)),await _});ce.finished.finally(()=>{g(void 0),T(void 0),d(void 0),p({isTransitioning:!1})}),T(ce)}},[f,m,n.window,H]),O.useEffect(()=>{m&&f&&o.location.key===f.location.key&&m.resolve()},[m,b,o.location,f]),O.useEffect(()=>{!y.isTransitioning&&M&&(d(M.state),p({isTransitioning:!0,flushSync:!1,currentLocation:M.currentLocation,nextLocation:M.nextLocation}),U(void 0))},[y.isTransitioning,M]);let J=O.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:$=>n.navigate($),push:($,_,ce)=>n.navigate($,{state:_,preventScrollReset:ce?.preventScrollReset}),replace:($,_,ce)=>n.navigate($,{replace:!0,state:_,preventScrollReset:ce?.preventScrollReset})}),[n]),F=n.basename||"/",ye=O.useMemo(()=>({router:n,navigator:J,static:!1,basename:F,unstable_onError:r}),[n,J,F,r]);return O.createElement(O.Fragment,null,O.createElement(en.Provider,{value:ye},O.createElement(tu.Provider,{value:o},O.createElement(qy.Provider,{value:Q.current},O.createElement(Vs.Provider,{value:y},O.createElement(Ob,{basename:F,location:o.location,navigationType:o.historyAction,navigator:J},O.createElement(Rb,{routes:n.routes,future:n.future,state:o,unstable_onError:r})))))),null)}var Rb=O.memo(Tb);function Tb({routes:n,future:i,state:r,unstable_onError:o}){return rb(n,void 0,r,o,i)}function Ob({basename:n="/",children:i=null,location:r,navigationType:o="POP",navigator:s,static:f=!1}){Ne(!au(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=n.replace(/^\/*/,"/"),y=O.useMemo(()=>({basename:d,navigator:s,static:f,future:{}}),[d,s,f]);typeof r=="string"&&(r=Ol(r));let{pathname:p="/",search:m="",hash:g="",state:b=null,key:T="default"}=r,M=O.useMemo(()=>{let U=na(p,d);return U==null?null:{location:{pathname:U,search:m,hash:g,state:b,key:T},navigationType:o}},[d,p,m,g,b,T,o]);return dt(M!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${g}" because it does not start with the basename, so the <Router> won't render anything.`),M==null?null:O.createElement(Oa.Provider,{value:y},O.createElement(Jr.Provider,{children:i,value:M}))}var Ur="get",Lr="application/x-www-form-urlencoded";function Fr(n){return n!=null&&typeof n.tagName=="string"}function Ab(n){return Fr(n)&&n.tagName.toLowerCase()==="button"}function wb(n){return Fr(n)&&n.tagName.toLowerCase()==="form"}function Db(n){return Fr(n)&&n.tagName.toLowerCase()==="input"}function Mb(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function Nb(n,i){return n.button===0&&(!i||i==="_self")&&!Mb(n)}var Nr=null;function _b(){if(Nr===null)try{new FormData(document.createElement("form"),0),Nr=!1}catch{Nr=!0}return Nr}var zb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function xs(n){return n!=null&&!zb.has(n)?(dt(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Lr}"`),null):n}function Cb(n,i){let r,o,s,f,d;if(wb(n)){let y=n.getAttribute("action");o=y?na(y,i):null,r=n.getAttribute("method")||Ur,s=xs(n.getAttribute("enctype"))||Lr,f=new FormData(n)}else if(Ab(n)||Db(n)&&(n.type==="submit"||n.type==="image")){let y=n.form;if(y==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=n.getAttribute("formaction")||y.getAttribute("action");if(o=p?na(p,i):null,r=n.getAttribute("formmethod")||y.getAttribute("method")||Ur,s=xs(n.getAttribute("formenctype"))||xs(y.getAttribute("enctype"))||Lr,f=new FormData(y,n),!_b()){let{name:m,type:g,value:b}=n;if(g==="image"){let T=m?`${m}.`:"";f.append(`${T}x`,"0"),f.append(`${T}y`,"0")}else m&&f.append(m,b)}}else{if(Fr(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Ur,o=null,s=Lr,d=n}return f&&s==="text/plain"&&(d=f,f=void 0),{action:o,method:r.toLowerCase(),encType:s,formData:f,body:d}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function ks(n,i){if(n===!1||n===null||typeof n>"u")throw new Error(i)}function Ub(n,i,r){let o=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return o.pathname==="/"?o.pathname=`_root.${r}`:i&&na(o.pathname,i)==="/"?o.pathname=`${i.replace(/\/$/,"")}/_root.${r}`:o.pathname=`${o.pathname.replace(/\/$/,"")}.${r}`,o}async function Lb(n,i){if(n.id in i)return i[n.id];try{let r=await import(n.module);return i[n.id]=r,r}catch(r){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function jb(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function Hb(n,i,r){let o=await Promise.all(n.map(async s=>{let f=i.routes[s.route.id];if(f){let d=await Lb(f,r);return d.links?d.links():[]}return[]}));return Gb(o.flat(1).filter(jb).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function ry(n,i,r,o,s,f){let d=(p,m)=>r[m]?p.route.id!==r[m].route.id:!0,y=(p,m)=>r[m].pathname!==p.pathname||r[m].route.path?.endsWith("*")&&r[m].params["*"]!==p.params["*"];return f==="assets"?i.filter((p,m)=>d(p,m)||y(p,m)):f==="data"?i.filter((p,m)=>{let g=o.routes[p.route.id];if(!g||!g.hasLoader)return!1;if(d(p,m)||y(p,m))return!0;if(p.route.shouldRevalidate){let b=p.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(n,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof b=="boolean")return b}return!0}):[]}function Bb(n,i,{includeHydrateFallback:r}={}){return qb(n.map(o=>{let s=i.routes[o.route.id];if(!s)return[];let f=[s.module];return s.clientActionModule&&(f=f.concat(s.clientActionModule)),s.clientLoaderModule&&(f=f.concat(s.clientLoaderModule)),r&&s.hydrateFallbackModule&&(f=f.concat(s.hydrateFallbackModule)),s.imports&&(f=f.concat(s.imports)),f}).flat(1))}function qb(n){return[...new Set(n)]}function Yb(n){let i={},r=Object.keys(n).sort();for(let o of r)i[o]=n[o];return i}function Gb(n,i){let r=new Set;return new Set(i),n.reduce((o,s)=>{let f=JSON.stringify(Yb(s));return r.has(f)||(r.add(f),o.push({key:f,link:s})),o},[])}function Vy(){let n=O.useContext(en);return ks(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function Xb(){let n=O.useContext(tu);return ks(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Js=O.createContext(void 0);Js.displayName="FrameworkContext";function Qy(){let n=O.useContext(Js);return ks(n,"You must render this element inside a <HydratedRouter> element"),n}function Vb(n,i){let r=O.useContext(Js),[o,s]=O.useState(!1),[f,d]=O.useState(!1),{onFocus:y,onBlur:p,onMouseEnter:m,onMouseLeave:g,onTouchStart:b}=i,T=O.useRef(null);O.useEffect(()=>{if(n==="render"&&d(!0),n==="viewport"){let Q=K=>{K.forEach(J=>{d(J.isIntersecting)})},H=new IntersectionObserver(Q,{threshold:.5});return T.current&&H.observe(T.current),()=>{H.disconnect()}}},[n]),O.useEffect(()=>{if(o){let Q=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(Q)}}},[o]);let M=()=>{s(!0)},U=()=>{s(!1),d(!1)};return r?n!=="intent"?[f,T,{}]:[f,T,{onFocus:Ji(y,M),onBlur:Ji(p,U),onMouseEnter:Ji(m,M),onMouseLeave:Ji(g,U),onTouchStart:Ji(b,M)}]:[!1,T,{}]}function Ji(n,i){return r=>{n&&n(r),r.defaultPrevented||i(r)}}function Qb({page:n,...i}){let{router:r}=Vy(),o=O.useMemo(()=>El(r.routes,n,r.basename),[r.routes,n,r.basename]);return o?O.createElement(Kb,{page:n,matches:o,...i}):null}function Zb(n){let{manifest:i,routeModules:r}=Qy(),[o,s]=O.useState([]);return O.useEffect(()=>{let f=!1;return Hb(n,i,r).then(d=>{f||s(d)}),()=>{f=!0}},[n,i,r]),o}function Kb({page:n,matches:i,...r}){let o=tn(),{manifest:s,routeModules:f}=Qy(),{basename:d}=Vy(),{loaderData:y,matches:p}=Xb(),m=O.useMemo(()=>ry(n,i,p,s,o,"data"),[n,i,p,s,o]),g=O.useMemo(()=>ry(n,i,p,s,o,"assets"),[n,i,p,s,o]),b=O.useMemo(()=>{if(n===o.pathname+o.search+o.hash)return[];let U=new Set,Q=!1;if(i.forEach(K=>{let J=s.routes[K.route.id];!J||!J.hasLoader||(!m.some(F=>F.route.id===K.route.id)&&K.route.id in y&&f[K.route.id]?.shouldRevalidate||J.hasClientLoader?Q=!0:U.add(K.route.id))}),U.size===0)return[];let H=Ub(n,d,"data");return Q&&U.size>0&&H.searchParams.set("_routes",i.filter(K=>U.has(K.route.id)).map(K=>K.route.id).join(",")),[H.pathname+H.search]},[d,y,o,s,m,i,n,f]),T=O.useMemo(()=>Bb(g,s),[g,s]),M=Zb(g);return O.createElement(O.Fragment,null,b.map(U=>O.createElement("link",{key:U,rel:"prefetch",as:"fetch",href:U,...r})),T.map(U=>O.createElement("link",{key:U,rel:"modulepreload",href:U,...r})),M.map(({key:U,link:Q})=>O.createElement("link",{key:U,nonce:r.nonce,...Q})))}function kb(...n){return i=>{n.forEach(r=>{typeof r=="function"?r(i):r!=null&&(r.current=i)})}}var Zy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Zy&&(window.__reactRouterVersion="7.8.2")}catch{}function Jb(n,i){return Ug({basename:i?.basename,unstable_getContext:i?.unstable_getContext,future:i?.future,history:Iv({window:i?.window}),hydrationData:Fb(),routes:n,mapRouteProperties:bb,hydrationRouteProperties:Sb,dataStrategy:i?.dataStrategy,patchRoutesOnNavigation:i?.patchRoutesOnNavigation,window:i?.window}).initialize()}function Fb(){let n=window?.__staticRouterHydrationData;return n&&n.errors&&(n={...n,errors:$b(n.errors)}),n}function $b(n){if(!n)return null;let i=Object.entries(n),r={};for(let[o,s]of i)if(s&&s.__type==="RouteErrorResponse")r[o]=new Gr(s.status,s.statusText,s.data,s.internal===!0);else if(s&&s.__type==="Error"){if(s.__subType){let f=window[s.__subType];if(typeof f=="function")try{let d=new f(s.message);d.stack="",r[o]=d}catch{}}if(r[o]==null){let f=new Error(s.message);f.stack="",r[o]=f}}else r[o]=s;return r}var Ky=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,an=O.forwardRef(function({onClick:i,discover:r="render",prefetch:o="none",relative:s,reloadDocument:f,replace:d,state:y,target:p,to:m,preventScrollReset:g,viewTransition:b,...T},M){let{basename:U}=O.useContext(Oa),Q=typeof m=="string"&&Ky.test(m),H,K=!1;if(typeof m=="string"&&Q&&(H=m,Zy))try{let pe=new URL(window.location.href),Ue=m.startsWith("//")?new URL(pe.protocol+m):new URL(m),Pe=na(Ue.pathname,U);Ue.origin===pe.origin&&Pe!=null?m=Pe+Ue.search+Ue.hash:K=!0}catch{dt(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let J=nb(m,{relative:s}),[F,ye,$]=Vb(o,T),_=e1(m,{replace:d,state:y,target:p,preventScrollReset:g,relative:s,viewTransition:b});function ce(pe){i&&i(pe),pe.defaultPrevented||_(pe)}let ge=O.createElement("a",{...T,...$,href:H||J,onClick:K||f?i:ce,ref:kb(M,ye),target:p,"data-discover":!Q&&r==="render"?"true":void 0});return F&&!Q?O.createElement(O.Fragment,null,ge,O.createElement(Qb,{page:J})):ge});an.displayName="Link";var Wb=O.forwardRef(function({"aria-current":i="page",caseSensitive:r=!1,className:o="",end:s=!1,style:f,to:d,viewTransition:y,children:p,...m},g){let b=lu(d,{relative:m.relative}),T=tn(),M=O.useContext(tu),{navigator:U,basename:Q}=O.useContext(Oa),H=M!=null&&i1(b)&&y===!0,K=U.encodeLocation?U.encodeLocation(b).pathname:b.pathname,J=T.pathname,F=M&&M.navigation&&M.navigation.location?M.navigation.location.pathname:null;r||(J=J.toLowerCase(),F=F?F.toLowerCase():null,K=K.toLowerCase()),F&&Q&&(F=na(F,Q)||F);const ye=K!=="/"&&K.endsWith("/")?K.length-1:K.length;let $=J===K||!s&&J.startsWith(K)&&J.charAt(ye)==="/",_=F!=null&&(F===K||!s&&F.startsWith(K)&&F.charAt(K.length)==="/"),ce={isActive:$,isPending:_,isTransitioning:H},ge=$?i:void 0,pe;typeof o=="function"?pe=o(ce):pe=[o,$?"active":null,_?"pending":null,H?"transitioning":null].filter(Boolean).join(" ");let Ue=typeof f=="function"?f(ce):f;return O.createElement(an,{...m,"aria-current":ge,className:pe,ref:g,style:Ue,to:d,viewTransition:y},typeof p=="function"?p(ce):p)});Wb.displayName="NavLink";var Pb=O.forwardRef(({discover:n="render",fetcherKey:i,navigate:r,reloadDocument:o,replace:s,state:f,method:d=Ur,action:y,onSubmit:p,relative:m,preventScrollReset:g,viewTransition:b,...T},M)=>{let U=l1(),Q=n1(y,{relative:m}),H=d.toLowerCase()==="get"?"get":"post",K=typeof y=="string"&&Ky.test(y),J=F=>{if(p&&p(F),F.defaultPrevented)return;F.preventDefault();let ye=F.nativeEvent.submitter,$=ye?.getAttribute("formmethod")||d;U(ye||F.currentTarget,{fetcherKey:i,method:$,navigate:r,replace:s,state:f,relative:m,preventScrollReset:g,viewTransition:b})};return O.createElement("form",{ref:M,method:H,action:Q,onSubmit:o?p:J,...T,"data-discover":!K&&n==="render"?"true":void 0})});Pb.displayName="Form";function Ib(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ky(n){let i=O.useContext(en);return Ne(i,Ib(n)),i}function e1(n,{target:i,replace:r,state:o,preventScrollReset:s,relative:f,viewTransition:d}={}){let y=Zn(),p=tn(),m=lu(n,{relative:f});return O.useCallback(g=>{if(Nb(g,i)){g.preventDefault();let b=r!==void 0?r:Tl(p)===Tl(m);y(n,{replace:b,state:o,preventScrollReset:s,relative:f,viewTransition:d})}},[p,y,m,r,o,i,n,s,f,d])}var t1=0,a1=()=>`__${String(++t1)}__`;function l1(){let{router:n}=ky("useSubmit"),{basename:i}=O.useContext(Oa),r=pb();return O.useCallback(async(o,s={})=>{let{action:f,method:d,encType:y,formData:p,body:m}=Cb(o,i);if(s.navigate===!1){let g=s.fetcherKey||a1();await n.fetch(g,r,s.action||f,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||y,flushSync:s.flushSync})}else await n.navigate(s.action||f,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||y,replace:s.replace,state:s.state,fromRouteId:r,flushSync:s.flushSync,viewTransition:s.viewTransition})},[n,i,r])}function n1(n,{relative:i}={}){let{basename:r}=O.useContext(Oa),o=O.useContext(Aa);Ne(o,"useFormAction must be used inside a RouteContext");let[s]=o.matches.slice(-1),f={...lu(n||".",{relative:i})},d=tn();if(n==null){f.search=d.search;let y=new URLSearchParams(f.search),p=y.getAll("index");if(p.some(g=>g==="")){y.delete("index"),p.filter(b=>b).forEach(b=>y.append("index",b));let g=y.toString();f.search=g?`?${g}`:""}}return(!n||n===".")&&s.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:Ta([r,f.pathname])),Tl(f)}function i1(n,{relative:i}={}){let r=O.useContext(Vs);Ne(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=ky("useViewTransitionState"),s=lu(n,{relative:i});if(!r.isTransitioning)return!1;let f=na(r.currentLocation.pathname,o)||r.currentLocation.pathname,d=na(r.nextLocation.pathname,o)||r.nextLocation.pathname;return Yr(s.pathname,d)!=null||Yr(s.pathname,f)!=null}var Jy={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},oy=Wl.createContext&&Wl.createContext(Jy),u1=["attr","size","title"];function r1(n,i){if(n==null)return{};var r=o1(n,i),o,s;if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(n);for(s=0;s<f.length;s++)o=f[s],!(i.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(n,o)&&(r[o]=n[o])}return r}function o1(n,i){if(n==null)return{};var r={};for(var o in n)if(Object.prototype.hasOwnProperty.call(n,o)){if(i.indexOf(o)>=0)continue;r[o]=n[o]}return r}function Vr(){return Vr=Object.assign?Object.assign.bind():function(n){for(var i=1;i<arguments.length;i++){var r=arguments[i];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(n[o]=r[o])}return n},Vr.apply(this,arguments)}function cy(n,i){var r=Object.keys(n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);i&&(o=o.filter(function(s){return Object.getOwnPropertyDescriptor(n,s).enumerable})),r.push.apply(r,o)}return r}function Qr(n){for(var i=1;i<arguments.length;i++){var r=arguments[i]!=null?arguments[i]:{};i%2?cy(Object(r),!0).forEach(function(o){c1(n,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):cy(Object(r)).forEach(function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(r,o))})}return n}function c1(n,i,r){return i=s1(i),i in n?Object.defineProperty(n,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[i]=r,n}function s1(n){var i=f1(n,"string");return typeof i=="symbol"?i:i+""}function f1(n,i){if(typeof n!="object"||!n)return n;var r=n[Symbol.toPrimitive];if(r!==void 0){var o=r.call(n,i);if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(n)}function Fy(n){return n&&n.map((i,r)=>Wl.createElement(i.tag,Qr({key:r},i.attr),Fy(i.child)))}function fa(n){return i=>Wl.createElement(d1,Vr({attr:Qr({},n.attr)},i),Fy(n.child))}function d1(n){var i=r=>{var{attr:o,size:s,title:f}=n,d=r1(n,u1),y=s||r.size||"1em",p;return r.className&&(p=r.className),n.className&&(p=(p?p+" ":"")+n.className),Wl.createElement("svg",Vr({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,o,d,{className:p,style:Qr(Qr({color:n.color||r.color},r.style),n.style),height:y,width:y,xmlns:"http://www.w3.org/2000/svg"}),f&&Wl.createElement("title",null,f),n.children)};return oy!==void 0?Wl.createElement(oy.Consumer,null,r=>i(r)):i(Jy)}function h1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"},child:[]}]})(n)}function m1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"},child:[]},{tag:"line",attr:{x1:"12",x2:"12",y1:"16",y2:"12"},child:[]},{tag:"line",attr:{x1:"12",x2:"12.01",y1:"8",y2:"8"},child:[]}]})(n)}function y1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"},child:[]},{tag:"line",attr:{x1:"15",x2:"9",y1:"9",y2:"15"},child:[]},{tag:"line",attr:{x1:"9",x2:"15",y1:"9",y2:"15"},child:[]}]})(n)}function p1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"},child:[]},{tag:"path",attr:{d:"M14 2v4a2 2 0 0 0 2 2h4"},child:[]},{tag:"path",attr:{d:"M3 15h6"},child:[]},{tag:"path",attr:{d:"M6 12v6"},child:[]}]})(n)}function $y(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 12a1 1 0 0 1-10 0 1 1 0 0 0-10 0"},child:[]},{tag:"path",attr:{d:"M7 20.7a1 1 0 1 1 5-8.7 1 1 0 1 0 5-8.6"},child:[]},{tag:"path",attr:{d:"M7 3.3a1 1 0 1 1 5 8.6 1 1 0 1 0 5 8.6"},child:[]},{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]}]})(n)}function Wy(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M6 8L2 12L6 16"},child:[]},{tag:"path",attr:{d:"M2 12H22"},child:[]}]})(n)}function Py(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M2 6h4"},child:[]},{tag:"path",attr:{d:"M2 10h4"},child:[]},{tag:"path",attr:{d:"M2 14h4"},child:[]},{tag:"path",attr:{d:"M2 18h4"},child:[]},{tag:"rect",attr:{width:"16",height:"20",x:"4",y:"2",rx:"2"},child:[]},{tag:"path",attr:{d:"M15 2v20"},child:[]},{tag:"path",attr:{d:"M15 7h5"},child:[]},{tag:"path",attr:{d:"M15 12h5"},child:[]},{tag:"path",attr:{d:"M15 17h5"},child:[]}]})(n)}function Iy(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M5 12h14"},child:[]},{tag:"path",attr:{d:"M12 5v14"},child:[]}]})(n)}function v1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M10 2v3a1 1 0 0 0 1 1h5"},child:[]},{tag:"path",attr:{d:"M18 18v-6a1 1 0 0 0-1-1h-6a1 1 0 0 0-1 1v6"},child:[]},{tag:"path",attr:{d:"M18 22H4a2 2 0 0 1-2-2V6"},child:[]},{tag:"path",attr:{d:"M8 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9.172a2 2 0 0 1 1.414.586l2.828 2.828A2 2 0 0 1 22 6.828V16a2 2 0 0 1-2.01 2z"},child:[]}]})(n)}function g1(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"},child:[]},{tag:"path",attr:{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"},child:[]}]})(n)}function e0(n){return fa({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M3 6h18"},child:[]},{tag:"path",attr:{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"},child:[]},{tag:"path",attr:{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"},child:[]},{tag:"line",attr:{x1:"10",x2:"10",y1:"11",y2:"17"},child:[]},{tag:"line",attr:{x1:"14",x2:"14",y1:"11",y2:"17"},child:[]}]})(n)}let b1={data:""},S1=n=>typeof window=="object"?((n?n.querySelector("#_goober"):window._goober)||Object.assign((n||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:n||b1,E1=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,x1=/\/\*[^]*?\*\/|  +/g,sy=/\n+/g,Rl=(n,i)=>{let r="",o="",s="";for(let f in n){let d=n[f];f[0]=="@"?f[1]=="i"?r=f+" "+d+";":o+=f[1]=="f"?Rl(d,f):f+"{"+Rl(d,f[1]=="k"?"":i)+"}":typeof d=="object"?o+=Rl(d,i?i.replace(/([^,])+/g,y=>f.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,p=>/&/.test(p)?p.replace(/&/g,y):y?y+" "+p:p)):f):d!=null&&(f=/^--/.test(f)?f:f.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=Rl.p?Rl.p(f,d):f+":"+d+";")}return r+(i&&s?i+"{"+s+"}":s)+o},ka={},t0=n=>{if(typeof n=="object"){let i="";for(let r in n)i+=r+t0(n[r]);return i}return n},R1=(n,i,r,o,s)=>{let f=t0(n),d=ka[f]||(ka[f]=(p=>{let m=0,g=11;for(;m<p.length;)g=101*g+p.charCodeAt(m++)>>>0;return"go"+g})(f));if(!ka[d]){let p=f!==n?n:(m=>{let g,b,T=[{}];for(;g=E1.exec(m.replace(x1,""));)g[4]?T.shift():g[3]?(b=g[3].replace(sy," ").trim(),T.unshift(T[0][b]=T[0][b]||{})):T[0][g[1]]=g[2].replace(sy," ").trim();return T[0]})(n);ka[d]=Rl(s?{["@keyframes "+d]:p}:p,r?"":"."+d)}let y=r&&ka.g?ka.g:null;return r&&(ka.g=ka[d]),((p,m,g,b)=>{b?m.data=m.data.replace(b,p):m.data.indexOf(p)===-1&&(m.data=g?p+m.data:m.data+p)})(ka[d],i,o,y),d},T1=(n,i,r)=>n.reduce((o,s,f)=>{let d=i[f];if(d&&d.call){let y=d(r),p=y&&y.props&&y.props.className||/^go/.test(y)&&y;d=p?"."+p:y&&typeof y=="object"?y.props?"":Rl(y,""):y===!1?"":y}return o+s+(d??"")},"");function $r(n){let i=this||{},r=n.call?n(i.p):n;return R1(r.unshift?r.raw?T1(r,[].slice.call(arguments,1),i.p):r.reduce((o,s)=>Object.assign(o,s&&s.call?s(i.p):s),{}):r,S1(i.target),i.g,i.o,i.k)}let a0,Ns,_s;$r.bind({g:1});let Ja=$r.bind({k:1});function O1(n,i,r,o){Rl.p=i,a0=n,Ns=r,_s=o}function Al(n,i){let r=this||{};return function(){let o=arguments;function s(f,d){let y=Object.assign({},f),p=y.className||s.className;r.p=Object.assign({theme:Ns&&Ns()},y),r.o=/ *go\d+/.test(p),y.className=$r.apply(r,o)+(p?" "+p:"");let m=n;return n[0]&&(m=y.as||n,delete y.as),_s&&m[0]&&_s(y),a0(m,y)}return s}}var A1=n=>typeof n=="function",Zr=(n,i)=>A1(n)?n(i):n,w1=(()=>{let n=0;return()=>(++n).toString()})(),l0=(()=>{let n;return()=>{if(n===void 0&&typeof window<"u"){let i=matchMedia("(prefers-reduced-motion: reduce)");n=!i||i.matches}return n}})(),D1=20,Fs="default",n0=(n,i)=>{let{toastLimit:r}=n.settings;switch(i.type){case 0:return{...n,toasts:[i.toast,...n.toasts].slice(0,r)};case 1:return{...n,toasts:n.toasts.map(d=>d.id===i.toast.id?{...d,...i.toast}:d)};case 2:let{toast:o}=i;return n0(n,{type:n.toasts.find(d=>d.id===o.id)?1:0,toast:o});case 3:let{toastId:s}=i;return{...n,toasts:n.toasts.map(d=>d.id===s||s===void 0?{...d,dismissed:!0,visible:!1}:d)};case 4:return i.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(d=>d.id!==i.toastId)};case 5:return{...n,pausedAt:i.time};case 6:let f=i.time-(n.pausedAt||0);return{...n,pausedAt:void 0,toasts:n.toasts.map(d=>({...d,pauseDuration:d.pauseDuration+f}))}}},jr=[],i0={toasts:[],pausedAt:void 0,settings:{toastLimit:D1}},Ra={},u0=(n,i=Fs)=>{Ra[i]=n0(Ra[i]||i0,n),jr.forEach(([r,o])=>{r===i&&o(Ra[i])})},r0=n=>Object.keys(Ra).forEach(i=>u0(n,i)),M1=n=>Object.keys(Ra).find(i=>Ra[i].toasts.some(r=>r.id===n)),Wr=(n=Fs)=>i=>{u0(i,n)},N1={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},_1=(n={},i=Fs)=>{let[r,o]=O.useState(Ra[i]||i0),s=O.useRef(Ra[i]);O.useEffect(()=>(s.current!==Ra[i]&&o(Ra[i]),jr.push([i,o]),()=>{let d=jr.findIndex(([y])=>y===i);d>-1&&jr.splice(d,1)}),[i]);let f=r.toasts.map(d=>{var y,p,m;return{...n,...n[d.type],...d,removeDelay:d.removeDelay||((y=n[d.type])==null?void 0:y.removeDelay)||n?.removeDelay,duration:d.duration||((p=n[d.type])==null?void 0:p.duration)||n?.duration||N1[d.type],style:{...n.style,...(m=n[d.type])==null?void 0:m.style,...d.style}}});return{...r,toasts:f}},z1=(n,i="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:i,ariaProps:{role:"status","aria-live":"polite"},message:n,pauseDuration:0,...r,id:r?.id||w1()}),nu=n=>(i,r)=>{let o=z1(i,n,r);return Wr(o.toasterId||M1(o.id))({type:2,toast:o}),o.id},Ze=(n,i)=>nu("blank")(n,i);Ze.error=nu("error");Ze.success=nu("success");Ze.loading=nu("loading");Ze.custom=nu("custom");Ze.dismiss=(n,i)=>{let r={type:3,toastId:n};i?Wr(i)(r):r0(r)};Ze.dismissAll=n=>Ze.dismiss(void 0,n);Ze.remove=(n,i)=>{let r={type:4,toastId:n};i?Wr(i)(r):r0(r)};Ze.removeAll=n=>Ze.remove(void 0,n);Ze.promise=(n,i,r)=>{let o=Ze.loading(i.loading,{...r,...r?.loading});return typeof n=="function"&&(n=n()),n.then(s=>{let f=i.success?Zr(i.success,s):void 0;return f?Ze.success(f,{id:o,...r,...r?.success}):Ze.dismiss(o),s}).catch(s=>{let f=i.error?Zr(i.error,s):void 0;f?Ze.error(f,{id:o,...r,...r?.error}):Ze.dismiss(o)}),n};var C1=1e3,U1=(n,i="default")=>{let{toasts:r,pausedAt:o}=_1(n,i),s=O.useRef(new Map).current,f=O.useCallback((b,T=C1)=>{if(s.has(b))return;let M=setTimeout(()=>{s.delete(b),d({type:4,toastId:b})},T);s.set(b,M)},[]);O.useEffect(()=>{if(o)return;let b=Date.now(),T=r.map(M=>{if(M.duration===1/0)return;let U=(M.duration||0)+M.pauseDuration-(b-M.createdAt);if(U<0){M.visible&&Ze.dismiss(M.id);return}return setTimeout(()=>Ze.dismiss(M.id,i),U)});return()=>{T.forEach(M=>M&&clearTimeout(M))}},[r,o,i]);let d=O.useCallback(Wr(i),[i]),y=O.useCallback(()=>{d({type:5,time:Date.now()})},[d]),p=O.useCallback((b,T)=>{d({type:1,toast:{id:b,height:T}})},[d]),m=O.useCallback(()=>{o&&d({type:6,time:Date.now()})},[o,d]),g=O.useCallback((b,T)=>{let{reverseOrder:M=!1,gutter:U=8,defaultPosition:Q}=T||{},H=r.filter(F=>(F.position||Q)===(b.position||Q)&&F.height),K=H.findIndex(F=>F.id===b.id),J=H.filter((F,ye)=>ye<K&&F.visible).length;return H.filter(F=>F.visible).slice(...M?[J+1]:[0,J]).reduce((F,ye)=>F+(ye.height||0)+U,0)},[r]);return O.useEffect(()=>{r.forEach(b=>{if(b.dismissed)f(b.id,b.removeDelay);else{let T=s.get(b.id);T&&(clearTimeout(T),s.delete(b.id))}})},[r,f]),{toasts:r,handlers:{updateHeight:p,startPause:y,endPause:m,calculateOffset:g}}},L1=Ja`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,j1=Ja`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H1=Ja`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B1=Al("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${j1} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${n=>n.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H1} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,q1=Ja`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y1=Al("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${n=>n.secondary||"#e0e0e0"};
  border-right-color: ${n=>n.primary||"#616161"};
  animation: ${q1} 1s linear infinite;
`,G1=Ja`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,X1=Ja`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,V1=Al("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${X1} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${n=>n.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q1=Al("div")`
  position: absolute;
`,Z1=Al("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,K1=Ja`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,k1=Al("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${K1} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,J1=({toast:n})=>{let{icon:i,type:r,iconTheme:o}=n;return i!==void 0?typeof i=="string"?O.createElement(k1,null,i):i:r==="blank"?null:O.createElement(Z1,null,O.createElement(Y1,{...o}),r!=="loading"&&O.createElement(Q1,null,r==="error"?O.createElement(B1,{...o}):O.createElement(V1,{...o})))},F1=n=>`
0% {transform: translate3d(0,${n*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,$1=n=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${n*-150}%,-1px) scale(.6); opacity:0;}
`,W1="0%{opacity:0;} 100%{opacity:1;}",P1="0%{opacity:1;} 100%{opacity:0;}",I1=Al("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eS=Al("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,tS=(n,i)=>{let r=n.includes("top")?1:-1,[o,s]=l0()?[W1,P1]:[F1(r),$1(r)];return{animation:i?`${Ja(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ja(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},aS=O.memo(({toast:n,position:i,style:r,children:o})=>{let s=n.height?tS(n.position||i||"top-center",n.visible):{opacity:0},f=O.createElement(J1,{toast:n}),d=O.createElement(eS,{...n.ariaProps},Zr(n.message,n));return O.createElement(I1,{className:n.className,style:{...s,...r,...n.style}},typeof o=="function"?o({icon:f,message:d}):O.createElement(O.Fragment,null,f,d))});O1(O.createElement);var lS=({id:n,className:i,style:r,onHeightUpdate:o,children:s})=>{let f=O.useCallback(d=>{if(d){let y=()=>{let p=d.getBoundingClientRect().height;o(n,p)};y(),new MutationObserver(y).observe(d,{subtree:!0,childList:!0,characterData:!0})}},[n,o]);return O.createElement("div",{ref:f,className:i,style:r},s)},nS=(n,i)=>{let r=n.includes("top"),o=r?{top:0}:{bottom:0},s=n.includes("center")?{justifyContent:"center"}:n.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:l0()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${i*(r?1:-1)}px)`,...o,...s}},iS=$r`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,_r=16,uS=({reverseOrder:n,position:i="top-center",toastOptions:r,gutter:o,children:s,toasterId:f,containerStyle:d,containerClassName:y})=>{let{toasts:p,handlers:m}=U1(r,f);return O.createElement("div",{"data-rht-toaster":f||"",style:{position:"fixed",zIndex:9999,top:_r,left:_r,right:_r,bottom:_r,pointerEvents:"none",...d},className:y,onMouseEnter:m.startPause,onMouseLeave:m.endPause},p.map(g=>{let b=g.position||i,T=m.calculateOffset(g,{reverseOrder:n,gutter:o,defaultPosition:i}),M=nS(b,T);return O.createElement(lS,{id:g.id,key:g.id,onHeightUpdate:m.updateHeight,className:g.visible?iS:"",style:M},g.type==="custom"?Zr(g.message,g):s?s(g):O.createElement(aS,{toast:g,position:b}))}))};function o0(n,i){return function(){return n.apply(i,arguments)}}const{toString:rS}=Object.prototype,{getPrototypeOf:$s}=Object,{iterator:Pr,toStringTag:c0}=Symbol,Ir=(n=>i=>{const r=rS.call(i);return n[r]||(n[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),da=n=>(n=n.toLowerCase(),i=>Ir(i)===n),eo=n=>i=>typeof i===n,{isArray:Kn}=Array,eu=eo("undefined");function iu(n){return n!==null&&!eu(n)&&n.constructor!==null&&!eu(n.constructor)&&Lt(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const s0=da("ArrayBuffer");function oS(n){let i;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?i=ArrayBuffer.isView(n):i=n&&n.buffer&&s0(n.buffer),i}const cS=eo("string"),Lt=eo("function"),f0=eo("number"),uu=n=>n!==null&&typeof n=="object",sS=n=>n===!0||n===!1,Hr=n=>{if(Ir(n)!=="object")return!1;const i=$s(n);return(i===null||i===Object.prototype||Object.getPrototypeOf(i)===null)&&!(c0 in n)&&!(Pr in n)},fS=n=>{if(!uu(n)||iu(n))return!1;try{return Object.keys(n).length===0&&Object.getPrototypeOf(n)===Object.prototype}catch{return!1}},dS=da("Date"),hS=da("File"),mS=da("Blob"),yS=da("FileList"),pS=n=>uu(n)&&Lt(n.pipe),vS=n=>{let i;return n&&(typeof FormData=="function"&&n instanceof FormData||Lt(n.append)&&((i=Ir(n))==="formdata"||i==="object"&&Lt(n.toString)&&n.toString()==="[object FormData]"))},gS=da("URLSearchParams"),[bS,SS,ES,xS]=["ReadableStream","Request","Response","Headers"].map(da),RS=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ru(n,i,{allOwnKeys:r=!1}={}){if(n===null||typeof n>"u")return;let o,s;if(typeof n!="object"&&(n=[n]),Kn(n))for(o=0,s=n.length;o<s;o++)i.call(null,n[o],o,n);else{if(iu(n))return;const f=r?Object.getOwnPropertyNames(n):Object.keys(n),d=f.length;let y;for(o=0;o<d;o++)y=f[o],i.call(null,n[y],y,n)}}function d0(n,i){if(iu(n))return null;i=i.toLowerCase();const r=Object.keys(n);let o=r.length,s;for(;o-- >0;)if(s=r[o],i===s.toLowerCase())return s;return null}const $l=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,h0=n=>!eu(n)&&n!==$l;function zs(){const{caseless:n}=h0(this)&&this||{},i={},r=(o,s)=>{const f=n&&d0(i,s)||s;Hr(i[f])&&Hr(o)?i[f]=zs(i[f],o):Hr(o)?i[f]=zs({},o):Kn(o)?i[f]=o.slice():i[f]=o};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&ru(arguments[o],r);return i}const TS=(n,i,r,{allOwnKeys:o}={})=>(ru(i,(s,f)=>{r&&Lt(s)?n[f]=o0(s,r):n[f]=s},{allOwnKeys:o}),n),OS=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),AS=(n,i,r,o)=>{n.prototype=Object.create(i.prototype,o),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:i.prototype}),r&&Object.assign(n.prototype,r)},wS=(n,i,r,o)=>{let s,f,d;const y={};if(i=i||{},n==null)return i;do{for(s=Object.getOwnPropertyNames(n),f=s.length;f-- >0;)d=s[f],(!o||o(d,n,i))&&!y[d]&&(i[d]=n[d],y[d]=!0);n=r!==!1&&$s(n)}while(n&&(!r||r(n,i))&&n!==Object.prototype);return i},DS=(n,i,r)=>{n=String(n),(r===void 0||r>n.length)&&(r=n.length),r-=i.length;const o=n.indexOf(i,r);return o!==-1&&o===r},MS=n=>{if(!n)return null;if(Kn(n))return n;let i=n.length;if(!f0(i))return null;const r=new Array(i);for(;i-- >0;)r[i]=n[i];return r},NS=(n=>i=>n&&i instanceof n)(typeof Uint8Array<"u"&&$s(Uint8Array)),_S=(n,i)=>{const o=(n&&n[Pr]).call(n);let s;for(;(s=o.next())&&!s.done;){const f=s.value;i.call(n,f[0],f[1])}},zS=(n,i)=>{let r;const o=[];for(;(r=n.exec(i))!==null;)o.push(r);return o},CS=da("HTMLFormElement"),US=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,o,s){return o.toUpperCase()+s}),fy=(({hasOwnProperty:n})=>(i,r)=>n.call(i,r))(Object.prototype),LS=da("RegExp"),m0=(n,i)=>{const r=Object.getOwnPropertyDescriptors(n),o={};ru(r,(s,f)=>{let d;(d=i(s,f,n))!==!1&&(o[f]=d||s)}),Object.defineProperties(n,o)},jS=n=>{m0(n,(i,r)=>{if(Lt(n)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const o=n[r];if(Lt(o)){if(i.enumerable=!1,"writable"in i){i.writable=!1;return}i.set||(i.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},HS=(n,i)=>{const r={},o=s=>{s.forEach(f=>{r[f]=!0})};return Kn(n)?o(n):o(String(n).split(i)),r},BS=()=>{},qS=(n,i)=>n!=null&&Number.isFinite(n=+n)?n:i;function YS(n){return!!(n&&Lt(n.append)&&n[c0]==="FormData"&&n[Pr])}const GS=n=>{const i=new Array(10),r=(o,s)=>{if(uu(o)){if(i.indexOf(o)>=0)return;if(iu(o))return o;if(!("toJSON"in o)){i[s]=o;const f=Kn(o)?[]:{};return ru(o,(d,y)=>{const p=r(d,s+1);!eu(p)&&(f[y]=p)}),i[s]=void 0,f}}return o};return r(n,0)},XS=da("AsyncFunction"),VS=n=>n&&(uu(n)||Lt(n))&&Lt(n.then)&&Lt(n.catch),y0=((n,i)=>n?setImmediate:i?((r,o)=>($l.addEventListener("message",({source:s,data:f})=>{s===$l&&f===r&&o.length&&o.shift()()},!1),s=>{o.push(s),$l.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Lt($l.postMessage)),QS=typeof queueMicrotask<"u"?queueMicrotask.bind($l):typeof process<"u"&&process.nextTick||y0,ZS=n=>n!=null&&Lt(n[Pr]),B={isArray:Kn,isArrayBuffer:s0,isBuffer:iu,isFormData:vS,isArrayBufferView:oS,isString:cS,isNumber:f0,isBoolean:sS,isObject:uu,isPlainObject:Hr,isEmptyObject:fS,isReadableStream:bS,isRequest:SS,isResponse:ES,isHeaders:xS,isUndefined:eu,isDate:dS,isFile:hS,isBlob:mS,isRegExp:LS,isFunction:Lt,isStream:pS,isURLSearchParams:gS,isTypedArray:NS,isFileList:yS,forEach:ru,merge:zs,extend:TS,trim:RS,stripBOM:OS,inherits:AS,toFlatObject:wS,kindOf:Ir,kindOfTest:da,endsWith:DS,toArray:MS,forEachEntry:_S,matchAll:zS,isHTMLForm:CS,hasOwnProperty:fy,hasOwnProp:fy,reduceDescriptors:m0,freezeMethods:jS,toObjectSet:HS,toCamelCase:US,noop:BS,toFiniteNumber:qS,findKey:d0,global:$l,isContextDefined:h0,isSpecCompliantForm:YS,toJSONObject:GS,isAsyncFn:XS,isThenable:VS,setImmediate:y0,asap:QS,isIterable:ZS};function Ee(n,i,r,o,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",i&&(this.code=i),r&&(this.config=r),o&&(this.request=o),s&&(this.response=s,this.status=s.status?s.status:null)}B.inherits(Ee,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:B.toJSONObject(this.config),code:this.code,status:this.status}}});const p0=Ee.prototype,v0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{v0[n]={value:n}});Object.defineProperties(Ee,v0);Object.defineProperty(p0,"isAxiosError",{value:!0});Ee.from=(n,i,r,o,s,f)=>{const d=Object.create(p0);return B.toFlatObject(n,d,function(p){return p!==Error.prototype},y=>y!=="isAxiosError"),Ee.call(d,n.message,i,r,o,s),d.cause=n,d.name=n.name,f&&Object.assign(d,f),d};const KS=null;function Cs(n){return B.isPlainObject(n)||B.isArray(n)}function g0(n){return B.endsWith(n,"[]")?n.slice(0,-2):n}function dy(n,i,r){return n?n.concat(i).map(function(s,f){return s=g0(s),!r&&f?"["+s+"]":s}).join(r?".":""):i}function kS(n){return B.isArray(n)&&!n.some(Cs)}const JS=B.toFlatObject(B,{},null,function(i){return/^is[A-Z]/.test(i)});function to(n,i,r){if(!B.isObject(n))throw new TypeError("target must be an object");i=i||new FormData,r=B.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(Q,H){return!B.isUndefined(H[Q])});const o=r.metaTokens,s=r.visitor||g,f=r.dots,d=r.indexes,p=(r.Blob||typeof Blob<"u"&&Blob)&&B.isSpecCompliantForm(i);if(!B.isFunction(s))throw new TypeError("visitor must be a function");function m(U){if(U===null)return"";if(B.isDate(U))return U.toISOString();if(B.isBoolean(U))return U.toString();if(!p&&B.isBlob(U))throw new Ee("Blob is not supported. Use a Buffer instead.");return B.isArrayBuffer(U)||B.isTypedArray(U)?p&&typeof Blob=="function"?new Blob([U]):Buffer.from(U):U}function g(U,Q,H){let K=U;if(U&&!H&&typeof U=="object"){if(B.endsWith(Q,"{}"))Q=o?Q:Q.slice(0,-2),U=JSON.stringify(U);else if(B.isArray(U)&&kS(U)||(B.isFileList(U)||B.endsWith(Q,"[]"))&&(K=B.toArray(U)))return Q=g0(Q),K.forEach(function(F,ye){!(B.isUndefined(F)||F===null)&&i.append(d===!0?dy([Q],ye,f):d===null?Q:Q+"[]",m(F))}),!1}return Cs(U)?!0:(i.append(dy(H,Q,f),m(U)),!1)}const b=[],T=Object.assign(JS,{defaultVisitor:g,convertValue:m,isVisitable:Cs});function M(U,Q){if(!B.isUndefined(U)){if(b.indexOf(U)!==-1)throw Error("Circular reference detected in "+Q.join("."));b.push(U),B.forEach(U,function(K,J){(!(B.isUndefined(K)||K===null)&&s.call(i,K,B.isString(J)?J.trim():J,Q,T))===!0&&M(K,Q?Q.concat(J):[J])}),b.pop()}}if(!B.isObject(n))throw new TypeError("data must be an object");return M(n),i}function hy(n){const i={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(o){return i[o]})}function Ws(n,i){this._pairs=[],n&&to(n,this,i)}const b0=Ws.prototype;b0.append=function(i,r){this._pairs.push([i,r])};b0.toString=function(i){const r=i?function(o){return i.call(this,o,hy)}:hy;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function FS(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function S0(n,i,r){if(!i)return n;const o=r&&r.encode||FS;B.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let f;if(s?f=s(i,r):f=B.isURLSearchParams(i)?i.toString():new Ws(i,r).toString(o),f){const d=n.indexOf("#");d!==-1&&(n=n.slice(0,d)),n+=(n.indexOf("?")===-1?"?":"&")+f}return n}class my{constructor(){this.handlers=[]}use(i,r,o){return this.handlers.push({fulfilled:i,rejected:r,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(i){this.handlers[i]&&(this.handlers[i]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(i){B.forEach(this.handlers,function(o){o!==null&&i(o)})}}const E0={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},$S=typeof URLSearchParams<"u"?URLSearchParams:Ws,WS=typeof FormData<"u"?FormData:null,PS=typeof Blob<"u"?Blob:null,IS={isBrowser:!0,classes:{URLSearchParams:$S,FormData:WS,Blob:PS},protocols:["http","https","file","blob","url","data"]},Ps=typeof window<"u"&&typeof document<"u",Us=typeof navigator=="object"&&navigator||void 0,e2=Ps&&(!Us||["ReactNative","NativeScript","NS"].indexOf(Us.product)<0),t2=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",a2=Ps&&window.location.href||"http://localhost",l2=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ps,hasStandardBrowserEnv:e2,hasStandardBrowserWebWorkerEnv:t2,navigator:Us,origin:a2},Symbol.toStringTag,{value:"Module"})),Tt={...l2,...IS};function n2(n,i){return to(n,new Tt.classes.URLSearchParams,{visitor:function(r,o,s,f){return Tt.isNode&&B.isBuffer(r)?(this.append(o,r.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)},...i})}function i2(n){return B.matchAll(/\w+|\[(\w*)]/g,n).map(i=>i[0]==="[]"?"":i[1]||i[0])}function u2(n){const i={},r=Object.keys(n);let o;const s=r.length;let f;for(o=0;o<s;o++)f=r[o],i[f]=n[f];return i}function x0(n){function i(r,o,s,f){let d=r[f++];if(d==="__proto__")return!0;const y=Number.isFinite(+d),p=f>=r.length;return d=!d&&B.isArray(s)?s.length:d,p?(B.hasOwnProp(s,d)?s[d]=[s[d],o]:s[d]=o,!y):((!s[d]||!B.isObject(s[d]))&&(s[d]=[]),i(r,o,s[d],f)&&B.isArray(s[d])&&(s[d]=u2(s[d])),!y)}if(B.isFormData(n)&&B.isFunction(n.entries)){const r={};return B.forEachEntry(n,(o,s)=>{i(i2(o),s,r,0)}),r}return null}function r2(n,i,r){if(B.isString(n))try{return(i||JSON.parse)(n),B.trim(n)}catch(o){if(o.name!=="SyntaxError")throw o}return(r||JSON.stringify)(n)}const ou={transitional:E0,adapter:["xhr","http","fetch"],transformRequest:[function(i,r){const o=r.getContentType()||"",s=o.indexOf("application/json")>-1,f=B.isObject(i);if(f&&B.isHTMLForm(i)&&(i=new FormData(i)),B.isFormData(i))return s?JSON.stringify(x0(i)):i;if(B.isArrayBuffer(i)||B.isBuffer(i)||B.isStream(i)||B.isFile(i)||B.isBlob(i)||B.isReadableStream(i))return i;if(B.isArrayBufferView(i))return i.buffer;if(B.isURLSearchParams(i))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),i.toString();let y;if(f){if(o.indexOf("application/x-www-form-urlencoded")>-1)return n2(i,this.formSerializer).toString();if((y=B.isFileList(i))||o.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return to(y?{"files[]":i}:i,p&&new p,this.formSerializer)}}return f||s?(r.setContentType("application/json",!1),r2(i)):i}],transformResponse:[function(i){const r=this.transitional||ou.transitional,o=r&&r.forcedJSONParsing,s=this.responseType==="json";if(B.isResponse(i)||B.isReadableStream(i))return i;if(i&&B.isString(i)&&(o&&!this.responseType||s)){const d=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(i)}catch(y){if(d)throw y.name==="SyntaxError"?Ee.from(y,Ee.ERR_BAD_RESPONSE,this,null,this.response):y}}return i}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Tt.classes.FormData,Blob:Tt.classes.Blob},validateStatus:function(i){return i>=200&&i<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};B.forEach(["delete","get","head","post","put","patch"],n=>{ou.headers[n]={}});const o2=B.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),c2=n=>{const i={};let r,o,s;return n&&n.split(`
`).forEach(function(d){s=d.indexOf(":"),r=d.substring(0,s).trim().toLowerCase(),o=d.substring(s+1).trim(),!(!r||i[r]&&o2[r])&&(r==="set-cookie"?i[r]?i[r].push(o):i[r]=[o]:i[r]=i[r]?i[r]+", "+o:o)}),i},yy=Symbol("internals");function Fi(n){return n&&String(n).trim().toLowerCase()}function Br(n){return n===!1||n==null?n:B.isArray(n)?n.map(Br):String(n)}function s2(n){const i=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=r.exec(n);)i[o[1]]=o[2];return i}const f2=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Rs(n,i,r,o,s){if(B.isFunction(o))return o.call(this,i,r);if(s&&(i=r),!!B.isString(i)){if(B.isString(o))return i.indexOf(o)!==-1;if(B.isRegExp(o))return o.test(i)}}function d2(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(i,r,o)=>r.toUpperCase()+o)}function h2(n,i){const r=B.toCamelCase(" "+i);["get","set","has"].forEach(o=>{Object.defineProperty(n,o+r,{value:function(s,f,d){return this[o].call(this,i,s,f,d)},configurable:!0})})}let jt=class{constructor(i){i&&this.set(i)}set(i,r,o){const s=this;function f(y,p,m){const g=Fi(p);if(!g)throw new Error("header name must be a non-empty string");const b=B.findKey(s,g);(!b||s[b]===void 0||m===!0||m===void 0&&s[b]!==!1)&&(s[b||p]=Br(y))}const d=(y,p)=>B.forEach(y,(m,g)=>f(m,g,p));if(B.isPlainObject(i)||i instanceof this.constructor)d(i,r);else if(B.isString(i)&&(i=i.trim())&&!f2(i))d(c2(i),r);else if(B.isObject(i)&&B.isIterable(i)){let y={},p,m;for(const g of i){if(!B.isArray(g))throw TypeError("Object iterator must return a key-value pair");y[m=g[0]]=(p=y[m])?B.isArray(p)?[...p,g[1]]:[p,g[1]]:g[1]}d(y,r)}else i!=null&&f(r,i,o);return this}get(i,r){if(i=Fi(i),i){const o=B.findKey(this,i);if(o){const s=this[o];if(!r)return s;if(r===!0)return s2(s);if(B.isFunction(r))return r.call(this,s,o);if(B.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(i,r){if(i=Fi(i),i){const o=B.findKey(this,i);return!!(o&&this[o]!==void 0&&(!r||Rs(this,this[o],o,r)))}return!1}delete(i,r){const o=this;let s=!1;function f(d){if(d=Fi(d),d){const y=B.findKey(o,d);y&&(!r||Rs(o,o[y],y,r))&&(delete o[y],s=!0)}}return B.isArray(i)?i.forEach(f):f(i),s}clear(i){const r=Object.keys(this);let o=r.length,s=!1;for(;o--;){const f=r[o];(!i||Rs(this,this[f],f,i,!0))&&(delete this[f],s=!0)}return s}normalize(i){const r=this,o={};return B.forEach(this,(s,f)=>{const d=B.findKey(o,f);if(d){r[d]=Br(s),delete r[f];return}const y=i?d2(f):String(f).trim();y!==f&&delete r[f],r[y]=Br(s),o[y]=!0}),this}concat(...i){return this.constructor.concat(this,...i)}toJSON(i){const r=Object.create(null);return B.forEach(this,(o,s)=>{o!=null&&o!==!1&&(r[s]=i&&B.isArray(o)?o.join(", "):o)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([i,r])=>i+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(i){return i instanceof this?i:new this(i)}static concat(i,...r){const o=new this(i);return r.forEach(s=>o.set(s)),o}static accessor(i){const o=(this[yy]=this[yy]={accessors:{}}).accessors,s=this.prototype;function f(d){const y=Fi(d);o[y]||(h2(s,d),o[y]=!0)}return B.isArray(i)?i.forEach(f):f(i),this}};jt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);B.reduceDescriptors(jt.prototype,({value:n},i)=>{let r=i[0].toUpperCase()+i.slice(1);return{get:()=>n,set(o){this[r]=o}}});B.freezeMethods(jt);function Ts(n,i){const r=this||ou,o=i||r,s=jt.from(o.headers);let f=o.data;return B.forEach(n,function(y){f=y.call(r,f,s.normalize(),i?i.status:void 0)}),s.normalize(),f}function R0(n){return!!(n&&n.__CANCEL__)}function kn(n,i,r){Ee.call(this,n??"canceled",Ee.ERR_CANCELED,i,r),this.name="CanceledError"}B.inherits(kn,Ee,{__CANCEL__:!0});function T0(n,i,r){const o=r.config.validateStatus;!r.status||!o||o(r.status)?n(r):i(new Ee("Request failed with status code "+r.status,[Ee.ERR_BAD_REQUEST,Ee.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function m2(n){const i=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return i&&i[1]||""}function y2(n,i){n=n||10;const r=new Array(n),o=new Array(n);let s=0,f=0,d;return i=i!==void 0?i:1e3,function(p){const m=Date.now(),g=o[f];d||(d=m),r[s]=p,o[s]=m;let b=f,T=0;for(;b!==s;)T+=r[b++],b=b%n;if(s=(s+1)%n,s===f&&(f=(f+1)%n),m-d<i)return;const M=g&&m-g;return M?Math.round(T*1e3/M):void 0}}function p2(n,i){let r=0,o=1e3/i,s,f;const d=(m,g=Date.now())=>{r=g,s=null,f&&(clearTimeout(f),f=null),n(...m)};return[(...m)=>{const g=Date.now(),b=g-r;b>=o?d(m,g):(s=m,f||(f=setTimeout(()=>{f=null,d(s)},o-b)))},()=>s&&d(s)]}const Kr=(n,i,r=3)=>{let o=0;const s=y2(50,250);return p2(f=>{const d=f.loaded,y=f.lengthComputable?f.total:void 0,p=d-o,m=s(p),g=d<=y;o=d;const b={loaded:d,total:y,progress:y?d/y:void 0,bytes:p,rate:m||void 0,estimated:m&&y&&g?(y-d)/m:void 0,event:f,lengthComputable:y!=null,[i?"download":"upload"]:!0};n(b)},r)},py=(n,i)=>{const r=n!=null;return[o=>i[0]({lengthComputable:r,total:n,loaded:o}),i[1]]},vy=n=>(...i)=>B.asap(()=>n(...i)),v2=Tt.hasStandardBrowserEnv?((n,i)=>r=>(r=new URL(r,Tt.origin),n.protocol===r.protocol&&n.host===r.host&&(i||n.port===r.port)))(new URL(Tt.origin),Tt.navigator&&/(msie|trident)/i.test(Tt.navigator.userAgent)):()=>!0,g2=Tt.hasStandardBrowserEnv?{write(n,i,r,o,s,f){const d=[n+"="+encodeURIComponent(i)];B.isNumber(r)&&d.push("expires="+new Date(r).toGMTString()),B.isString(o)&&d.push("path="+o),B.isString(s)&&d.push("domain="+s),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(n){const i=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return i?decodeURIComponent(i[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function b2(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function S2(n,i){return i?n.replace(/\/?\/$/,"")+"/"+i.replace(/^\/+/,""):n}function O0(n,i,r){let o=!b2(i);return n&&(o||r==!1)?S2(n,i):i}const gy=n=>n instanceof jt?{...n}:n;function Il(n,i){i=i||{};const r={};function o(m,g,b,T){return B.isPlainObject(m)&&B.isPlainObject(g)?B.merge.call({caseless:T},m,g):B.isPlainObject(g)?B.merge({},g):B.isArray(g)?g.slice():g}function s(m,g,b,T){if(B.isUndefined(g)){if(!B.isUndefined(m))return o(void 0,m,b,T)}else return o(m,g,b,T)}function f(m,g){if(!B.isUndefined(g))return o(void 0,g)}function d(m,g){if(B.isUndefined(g)){if(!B.isUndefined(m))return o(void 0,m)}else return o(void 0,g)}function y(m,g,b){if(b in i)return o(m,g);if(b in n)return o(void 0,m)}const p={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:y,headers:(m,g,b)=>s(gy(m),gy(g),b,!0)};return B.forEach(Object.keys({...n,...i}),function(g){const b=p[g]||s,T=b(n[g],i[g],g);B.isUndefined(T)&&b!==y||(r[g]=T)}),r}const A0=n=>{const i=Il({},n);let{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:f,headers:d,auth:y}=i;i.headers=d=jt.from(d),i.url=S0(O0(i.baseURL,i.url,i.allowAbsoluteUrls),n.params,n.paramsSerializer),y&&d.set("Authorization","Basic "+btoa((y.username||"")+":"+(y.password?unescape(encodeURIComponent(y.password)):"")));let p;if(B.isFormData(r)){if(Tt.hasStandardBrowserEnv||Tt.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((p=d.getContentType())!==!1){const[m,...g]=p?p.split(";").map(b=>b.trim()).filter(Boolean):[];d.setContentType([m||"multipart/form-data",...g].join("; "))}}if(Tt.hasStandardBrowserEnv&&(o&&B.isFunction(o)&&(o=o(i)),o||o!==!1&&v2(i.url))){const m=s&&f&&g2.read(f);m&&d.set(s,m)}return i},E2=typeof XMLHttpRequest<"u",x2=E2&&function(n){return new Promise(function(r,o){const s=A0(n);let f=s.data;const d=jt.from(s.headers).normalize();let{responseType:y,onUploadProgress:p,onDownloadProgress:m}=s,g,b,T,M,U;function Q(){M&&M(),U&&U(),s.cancelToken&&s.cancelToken.unsubscribe(g),s.signal&&s.signal.removeEventListener("abort",g)}let H=new XMLHttpRequest;H.open(s.method.toUpperCase(),s.url,!0),H.timeout=s.timeout;function K(){if(!H)return;const F=jt.from("getAllResponseHeaders"in H&&H.getAllResponseHeaders()),$={data:!y||y==="text"||y==="json"?H.responseText:H.response,status:H.status,statusText:H.statusText,headers:F,config:n,request:H};T0(function(ce){r(ce),Q()},function(ce){o(ce),Q()},$),H=null}"onloadend"in H?H.onloadend=K:H.onreadystatechange=function(){!H||H.readyState!==4||H.status===0&&!(H.responseURL&&H.responseURL.indexOf("file:")===0)||setTimeout(K)},H.onabort=function(){H&&(o(new Ee("Request aborted",Ee.ECONNABORTED,n,H)),H=null)},H.onerror=function(){o(new Ee("Network Error",Ee.ERR_NETWORK,n,H)),H=null},H.ontimeout=function(){let ye=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const $=s.transitional||E0;s.timeoutErrorMessage&&(ye=s.timeoutErrorMessage),o(new Ee(ye,$.clarifyTimeoutError?Ee.ETIMEDOUT:Ee.ECONNABORTED,n,H)),H=null},f===void 0&&d.setContentType(null),"setRequestHeader"in H&&B.forEach(d.toJSON(),function(ye,$){H.setRequestHeader($,ye)}),B.isUndefined(s.withCredentials)||(H.withCredentials=!!s.withCredentials),y&&y!=="json"&&(H.responseType=s.responseType),m&&([T,U]=Kr(m,!0),H.addEventListener("progress",T)),p&&H.upload&&([b,M]=Kr(p),H.upload.addEventListener("progress",b),H.upload.addEventListener("loadend",M)),(s.cancelToken||s.signal)&&(g=F=>{H&&(o(!F||F.type?new kn(null,n,H):F),H.abort(),H=null)},s.cancelToken&&s.cancelToken.subscribe(g),s.signal&&(s.signal.aborted?g():s.signal.addEventListener("abort",g)));const J=m2(s.url);if(J&&Tt.protocols.indexOf(J)===-1){o(new Ee("Unsupported protocol "+J+":",Ee.ERR_BAD_REQUEST,n));return}H.send(f||null)})},R2=(n,i)=>{const{length:r}=n=n?n.filter(Boolean):[];if(i||r){let o=new AbortController,s;const f=function(m){if(!s){s=!0,y();const g=m instanceof Error?m:this.reason;o.abort(g instanceof Ee?g:new kn(g instanceof Error?g.message:g))}};let d=i&&setTimeout(()=>{d=null,f(new Ee(`timeout ${i} of ms exceeded`,Ee.ETIMEDOUT))},i);const y=()=>{n&&(d&&clearTimeout(d),d=null,n.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),n=null)};n.forEach(m=>m.addEventListener("abort",f));const{signal:p}=o;return p.unsubscribe=()=>B.asap(y),p}},T2=function*(n,i){let r=n.byteLength;if(r<i){yield n;return}let o=0,s;for(;o<r;)s=o+i,yield n.slice(o,s),o=s},O2=async function*(n,i){for await(const r of A2(n))yield*T2(r,i)},A2=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const i=n.getReader();try{for(;;){const{done:r,value:o}=await i.read();if(r)break;yield o}}finally{await i.cancel()}},by=(n,i,r,o)=>{const s=O2(n,i);let f=0,d,y=p=>{d||(d=!0,o&&o(p))};return new ReadableStream({async pull(p){try{const{done:m,value:g}=await s.next();if(m){y(),p.close();return}let b=g.byteLength;if(r){let T=f+=b;r(T)}p.enqueue(new Uint8Array(g))}catch(m){throw y(m),m}},cancel(p){return y(p),s.return()}},{highWaterMark:2})},ao=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",w0=ao&&typeof ReadableStream=="function",w2=ao&&(typeof TextEncoder=="function"?(n=>i=>n.encode(i))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),D0=(n,...i)=>{try{return!!n(...i)}catch{return!1}},D2=w0&&D0(()=>{let n=!1;const i=new Request(Tt.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!i}),Sy=64*1024,Ls=w0&&D0(()=>B.isReadableStream(new Response("").body)),kr={stream:Ls&&(n=>n.body)};ao&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(i=>{!kr[i]&&(kr[i]=B.isFunction(n[i])?r=>r[i]():(r,o)=>{throw new Ee(`Response type '${i}' is not supported`,Ee.ERR_NOT_SUPPORT,o)})})})(new Response);const M2=async n=>{if(n==null)return 0;if(B.isBlob(n))return n.size;if(B.isSpecCompliantForm(n))return(await new Request(Tt.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(B.isArrayBufferView(n)||B.isArrayBuffer(n))return n.byteLength;if(B.isURLSearchParams(n)&&(n=n+""),B.isString(n))return(await w2(n)).byteLength},N2=async(n,i)=>{const r=B.toFiniteNumber(n.getContentLength());return r??M2(i)},_2=ao&&(async n=>{let{url:i,method:r,data:o,signal:s,cancelToken:f,timeout:d,onDownloadProgress:y,onUploadProgress:p,responseType:m,headers:g,withCredentials:b="same-origin",fetchOptions:T}=A0(n);m=m?(m+"").toLowerCase():"text";let M=R2([s,f&&f.toAbortSignal()],d),U;const Q=M&&M.unsubscribe&&(()=>{M.unsubscribe()});let H;try{if(p&&D2&&r!=="get"&&r!=="head"&&(H=await N2(g,o))!==0){let $=new Request(i,{method:"POST",body:o,duplex:"half"}),_;if(B.isFormData(o)&&(_=$.headers.get("content-type"))&&g.setContentType(_),$.body){const[ce,ge]=py(H,Kr(vy(p)));o=by($.body,Sy,ce,ge)}}B.isString(b)||(b=b?"include":"omit");const K="credentials"in Request.prototype;U=new Request(i,{...T,signal:M,method:r.toUpperCase(),headers:g.normalize().toJSON(),body:o,duplex:"half",credentials:K?b:void 0});let J=await fetch(U,T);const F=Ls&&(m==="stream"||m==="response");if(Ls&&(y||F&&Q)){const $={};["status","statusText","headers"].forEach(pe=>{$[pe]=J[pe]});const _=B.toFiniteNumber(J.headers.get("content-length")),[ce,ge]=y&&py(_,Kr(vy(y),!0))||[];J=new Response(by(J.body,Sy,ce,()=>{ge&&ge(),Q&&Q()}),$)}m=m||"text";let ye=await kr[B.findKey(kr,m)||"text"](J,n);return!F&&Q&&Q(),await new Promise(($,_)=>{T0($,_,{data:ye,headers:jt.from(J.headers),status:J.status,statusText:J.statusText,config:n,request:U})})}catch(K){throw Q&&Q(),K&&K.name==="TypeError"&&/Load failed|fetch/i.test(K.message)?Object.assign(new Ee("Network Error",Ee.ERR_NETWORK,n,U),{cause:K.cause||K}):Ee.from(K,K&&K.code,n,U)}}),js={http:KS,xhr:x2,fetch:_2};B.forEach(js,(n,i)=>{if(n){try{Object.defineProperty(n,"name",{value:i})}catch{}Object.defineProperty(n,"adapterName",{value:i})}});const Ey=n=>`- ${n}`,z2=n=>B.isFunction(n)||n===null||n===!1,M0={getAdapter:n=>{n=B.isArray(n)?n:[n];const{length:i}=n;let r,o;const s={};for(let f=0;f<i;f++){r=n[f];let d;if(o=r,!z2(r)&&(o=js[(d=String(r)).toLowerCase()],o===void 0))throw new Ee(`Unknown adapter '${d}'`);if(o)break;s[d||"#"+f]=o}if(!o){const f=Object.entries(s).map(([y,p])=>`adapter ${y} `+(p===!1?"is not supported by the environment":"is not available in the build"));let d=i?f.length>1?`since :
`+f.map(Ey).join(`
`):" "+Ey(f[0]):"as no adapter specified";throw new Ee("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return o},adapters:js};function Os(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new kn(null,n)}function xy(n){return Os(n),n.headers=jt.from(n.headers),n.data=Ts.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),M0.getAdapter(n.adapter||ou.adapter)(n).then(function(o){return Os(n),o.data=Ts.call(n,n.transformResponse,o),o.headers=jt.from(o.headers),o},function(o){return R0(o)||(Os(n),o&&o.response&&(o.response.data=Ts.call(n,n.transformResponse,o.response),o.response.headers=jt.from(o.response.headers))),Promise.reject(o)})}const N0="1.11.0",lo={};["object","boolean","number","function","string","symbol"].forEach((n,i)=>{lo[n]=function(o){return typeof o===n||"a"+(i<1?"n ":" ")+n}});const Ry={};lo.transitional=function(i,r,o){function s(f,d){return"[Axios v"+N0+"] Transitional option '"+f+"'"+d+(o?". "+o:"")}return(f,d,y)=>{if(i===!1)throw new Ee(s(d," has been removed"+(r?" in "+r:"")),Ee.ERR_DEPRECATED);return r&&!Ry[d]&&(Ry[d]=!0,console.warn(s(d," has been deprecated since v"+r+" and will be removed in the near future"))),i?i(f,d,y):!0}};lo.spelling=function(i){return(r,o)=>(console.warn(`${o} is likely a misspelling of ${i}`),!0)};function C2(n,i,r){if(typeof n!="object")throw new Ee("options must be an object",Ee.ERR_BAD_OPTION_VALUE);const o=Object.keys(n);let s=o.length;for(;s-- >0;){const f=o[s],d=i[f];if(d){const y=n[f],p=y===void 0||d(y,f,n);if(p!==!0)throw new Ee("option "+f+" must be "+p,Ee.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Ee("Unknown option "+f,Ee.ERR_BAD_OPTION)}}const qr={assertOptions:C2,validators:lo},xa=qr.validators;let Pl=class{constructor(i){this.defaults=i||{},this.interceptors={request:new my,response:new my}}async request(i,r){try{return await this._request(i,r)}catch(o){if(o instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const f=s.stack?s.stack.replace(/^.+\n/,""):"";try{o.stack?f&&!String(o.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+f):o.stack=f}catch{}}throw o}}_request(i,r){typeof i=="string"?(r=r||{},r.url=i):r=i||{},r=Il(this.defaults,r);const{transitional:o,paramsSerializer:s,headers:f}=r;o!==void 0&&qr.assertOptions(o,{silentJSONParsing:xa.transitional(xa.boolean),forcedJSONParsing:xa.transitional(xa.boolean),clarifyTimeoutError:xa.transitional(xa.boolean)},!1),s!=null&&(B.isFunction(s)?r.paramsSerializer={serialize:s}:qr.assertOptions(s,{encode:xa.function,serialize:xa.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),qr.assertOptions(r,{baseUrl:xa.spelling("baseURL"),withXsrfToken:xa.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let d=f&&B.merge(f.common,f[r.method]);f&&B.forEach(["delete","get","head","post","put","patch","common"],U=>{delete f[U]}),r.headers=jt.concat(d,f);const y=[];let p=!0;this.interceptors.request.forEach(function(Q){typeof Q.runWhen=="function"&&Q.runWhen(r)===!1||(p=p&&Q.synchronous,y.unshift(Q.fulfilled,Q.rejected))});const m=[];this.interceptors.response.forEach(function(Q){m.push(Q.fulfilled,Q.rejected)});let g,b=0,T;if(!p){const U=[xy.bind(this),void 0];for(U.unshift(...y),U.push(...m),T=U.length,g=Promise.resolve(r);b<T;)g=g.then(U[b++],U[b++]);return g}T=y.length;let M=r;for(b=0;b<T;){const U=y[b++],Q=y[b++];try{M=U(M)}catch(H){Q.call(this,H);break}}try{g=xy.call(this,M)}catch(U){return Promise.reject(U)}for(b=0,T=m.length;b<T;)g=g.then(m[b++],m[b++]);return g}getUri(i){i=Il(this.defaults,i);const r=O0(i.baseURL,i.url,i.allowAbsoluteUrls);return S0(r,i.params,i.paramsSerializer)}};B.forEach(["delete","get","head","options"],function(i){Pl.prototype[i]=function(r,o){return this.request(Il(o||{},{method:i,url:r,data:(o||{}).data}))}});B.forEach(["post","put","patch"],function(i){function r(o){return function(f,d,y){return this.request(Il(y||{},{method:i,headers:o?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}Pl.prototype[i]=r(),Pl.prototype[i+"Form"]=r(!0)});let U2=class _0{constructor(i){if(typeof i!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(f){r=f});const o=this;this.promise.then(s=>{if(!o._listeners)return;let f=o._listeners.length;for(;f-- >0;)o._listeners[f](s);o._listeners=null}),this.promise.then=s=>{let f;const d=new Promise(y=>{o.subscribe(y),f=y}).then(s);return d.cancel=function(){o.unsubscribe(f)},d},i(function(f,d,y){o.reason||(o.reason=new kn(f,d,y),r(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(i){if(this.reason){i(this.reason);return}this._listeners?this._listeners.push(i):this._listeners=[i]}unsubscribe(i){if(!this._listeners)return;const r=this._listeners.indexOf(i);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const i=new AbortController,r=o=>{i.abort(o)};return this.subscribe(r),i.signal.unsubscribe=()=>this.unsubscribe(r),i.signal}static source(){let i;return{token:new _0(function(s){i=s}),cancel:i}}};function L2(n){return function(r){return n.apply(null,r)}}function j2(n){return B.isObject(n)&&n.isAxiosError===!0}const Hs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Hs).forEach(([n,i])=>{Hs[i]=n});function z0(n){const i=new Pl(n),r=o0(Pl.prototype.request,i);return B.extend(r,Pl.prototype,i,{allOwnKeys:!0}),B.extend(r,i,null,{allOwnKeys:!0}),r.create=function(s){return z0(Il(n,s))},r}const rt=z0(ou);rt.Axios=Pl;rt.CanceledError=kn;rt.CancelToken=U2;rt.isCancel=R0;rt.VERSION=N0;rt.toFormData=to;rt.AxiosError=Ee;rt.Cancel=rt.CanceledError;rt.all=function(i){return Promise.all(i)};rt.spread=L2;rt.isAxiosError=j2;rt.mergeConfig=Il;rt.AxiosHeaders=jt;rt.formToJSON=n=>x0(B.isHTMLForm(n)?new FormData(n):n);rt.getAdapter=M0.getAdapter;rt.HttpStatusCode=Hs;rt.default=rt;const{Axios:aE,AxiosError:lE,CanceledError:nE,isCancel:iE,CancelToken:uE,VERSION:rE,all:oE,Cancel:cE,isAxiosError:sE,spread:fE,toFormData:dE,AxiosHeaders:hE,HttpStatusCode:mE,formToJSON:yE,getAdapter:pE,mergeConfig:vE}=rt,cu=rt.create({baseURL:"http://localhost:5001/api",headers:{"Content-Type":"application/json"}}),H2=()=>{const[n,i]=O.useState(!1),[r,o]=O.useState(!0),[s,f]=O.useState(!1),[d,y]=O.useState([]);return O.useEffect(()=>{(async()=>{try{const m=await cu.get("/notes"),{data:g}=m;y(g),f(!1)}catch(m){console.log(m),f(!0),su(m,"Failed To Get Notes!",null,i)}finally{o(!1)}})()},[]),{isRateLimited:n,NotesData:d,isLoading:r,setNotesData:y,isError:s}},B2=()=>{const[n,i]=O.useState(!1),[r,o]=O.useState(!1),[s,f]=O.useState([]),d=Zn();return{isRateLimited:n,handlePost:async p=>{try{o(!0);const m=await cu.post("/notes",p),{data:g}=m;return f(g),Ze.success("Note Created Successfully!"),d("/"),g}catch(m){console.log(m),su(m,"Failed To Create Note!",()=>Ze.error("Slow Down, You're Creating Notes Too Fast!",{duration:5e3,icon:"💀",className:"text-sm font-bold"}),i)}finally{o(!1)}},isLoading:r,createdData:s,setCreatedData:f}},C0=()=>{const[n,i]=O.useState(!1),[r,o]=O.useState(!1),[s,f]=O.useState([]);return{isRateLimited:n,handleDelete:async y=>{try{o(!0);const p=await cu.delete(`notes/${y}`),{data:m}=p;return f(m),Ze.success("Note Deleted Successfully!"),m}catch(p){console.log(p),su(p,"Failed To Delete Note!",null,i)}finally{o(!1)}},isLoading:r,deletedData:s,setDeletedData:f}},q2=n=>{const[i,r]=O.useState(!1),[o,s]=O.useState(!0),[f,d]=O.useState([]),y=Zn();return O.useEffect(()=>{(async m=>{try{const g=await cu.get(`notes/${m}`),{data:b}=g;d(b)}catch(g){console.log(g),su(g,"Failed To Get Note!",()=>(y("/"),Ze.error("Slow Down, You Are Fetching The Note Too Fast!",{duration:5e3,icon:"💀",className:"text-sm font-bold"})),r)}finally{s(!1)}})(n)},[n,y]),{isRateLimited:i,NoteData:f,isLoading:o}},Y2=()=>{const[n,i]=O.useState(!1),[r,o]=O.useState(!1),[s,f]=O.useState([]),d=Zn();return{isRateLimited:n,handlePut:async(p,m)=>{try{o(!0);const g=await cu.put(`notes/${p}`,m),{data:b}=g;return f(b),Ze.success("Note Updated Successfully!"),d("/"),b}catch(g){console.log(g),su(g,"Failed To Update Note!",null,i)}finally{o(!1)}},isSaving:r,UpdatedData:s,setUpdatedData:f}},su=(n,i,r,o)=>{n?.response?.status===429?(console.log("Rate Limit Reached"),r&&r(),o(!0)):Ze.error(i)},Qn=({isLoading:n})=>n&&q.jsx($y,{size:16,className:"animate-spin text-primary"}),G2=({titleRef:n,contentRef:i,isLoading:r,handleSubmit:o})=>q.jsxs("div",{className:"w-full max-w-3xl mx-auto bg-base-300 rounded-lg p-6 md:p-8 mt-10",children:[q.jsxs("h1",{className:"text-xl md:text-2xl font-bold mb-4 flex items-center gap-2.5",children:[q.jsx("span",{children:"Create New Note "}),q.jsx(Py,{size:24,className:"text-primary"})]}),q.jsxs("form",{onSubmit:o,children:[q.jsxs("div",{className:"form-control w-full",children:[q.jsxs("label",{className:"label mb-2.5",children:[q.jsx(Qn,{isLoading:r}),q.jsx("span",{className:"label-text",children:"Title"})]}),q.jsx("input",{type:"text",ref:n,placeholder:"Note Title",className:"input rounded-lg input-bordered outline-none border-none input-primary w-full"})]}),q.jsxs("div",{className:"form-control w-full mt-4 flex flex-col",children:[q.jsxs("label",{className:"label mb-2.5",children:[q.jsx(Qn,{isLoading:r}),q.jsx("span",{className:"label-text",children:"Content"})]}),q.jsx("textarea",{ref:i,className:"textarea rounded-lg textarea-bordered h-48 w-full resize-none outline-none border-none textarea-primary",placeholder:"Write Your Note Here..."})]}),q.jsx("div",{className:"card-actions justify-end",children:q.jsx("button",{disabled:r,type:"submit",className:"btn btn-primary mt-5 flex gap-1.5 items-center",children:r?q.jsxs(q.Fragment,{children:[q.jsx(Qn,{isLoading:r})," ",q.jsx("span",{children:"Creating..."})]}):q.jsxs(q.Fragment,{children:[q.jsx(Iy,{size:16})," ",q.jsx("span",{children:"Create Note"})]})})})]})]}),X2=()=>{const n=O.useRef(null),i=O.useRef(null),{isLoading:r,handlePost:o}=B2(),s=async f=>{f.preventDefault();const d=n.current.value,y=i.current.value;if(!d.trim()||!y.trim()){Ze.error("Please Fill All The Fields",{icon:"✍️"});return}if(d.length<3){Ze.error("Title Should Be At least 3 Characters Long");return}try{await o({title:d,content:y}),n.current.value="",i.current.value=""}catch(p){console.log(p)}};return q.jsxs("section",{className:"py-10 container",children:[q.jsxs(an,{to:"/",className:"btn btn-ghost btn-md",children:[q.jsx(Wy,{size:24}),q.jsx("span",{children:"Back To Notes"})]}),q.jsx(G2,{contentRef:i,titleRef:n,isLoading:r,handleSubmit:f=>s(f)})]})},V2=()=>q.jsx("nav",{className:"container h-28 flex items-center bg-base-200 border-b border-base-content/20",children:q.jsxs("ul",{className:"flex items-center justify-between gap-10 w-full",children:[q.jsx("li",{children:q.jsx("h1",{className:"text-primary font-black text-lg sm:text-2xl tracking-wider",children:"ThinkBoard"})}),q.jsx("li",{children:q.jsxs(an,{to:"/create",className:"btn btn-primary font-bold text-sm",children:[q.jsx(Iy,{size:16})," ",q.jsx("span",{children:"New Note"})]})})]})});function Q2(n){return new Date(n).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}const Z2=({note:n,setNotesData:i})=>{const{_id:r,title:o,content:s,createdAt:f}=n,d=Zn(),{handleDelete:y}=C0();function p(m,g){m.preventDefault(),y(g),i(b=>b.filter(T=>T._id!==g))}return q.jsx(an,{to:`/note/${r}`,className:"card bg-base-300 border-t-6 border-primary/50 hover:border-primary shadow-primary/10 hover:shadow-xl main-transition",children:q.jsxs("div",{className:"card-body",children:[q.jsx("h3",{className:"text-lg md:text-xl font-bold card-title text-base-content tracking-wide",children:o}),q.jsx("p",{className:"text-sm text-base-content/50 tracking-wider mb-4",children:s}),q.jsxs("div",{className:"card-actions justify-between items-center gap-3",children:[q.jsx("p",{className:"text-xs tracking-wider text-base-content/50",children:Q2(f)}),q.jsxs("div",{className:"flex items-center gap-1",children:[q.jsx("button",{onClick:()=>d(`/note/${r}`),type:"button",className:"btn btn-ghost btn-xs",children:q.jsx(g1,{size:16})}),q.jsx("button",{onClick:m=>p(m,r),type:"button",className:"text-error btn btn-ghost btn-xs",children:q.jsx(e0,{size:16})})]})]})]})})},K2=()=>q.jsxs("div",{className:"flex items-center justify-center gap-2",children:[q.jsx($y,{size:32,className:"animate-spin text-primary"}),q.jsx("div",{className:"text-xl font-black tracking-widest uppercase text-primary animate-pulse",children:"Loading..."})]}),k2=()=>q.jsx("div",{className:"container py-8",children:q.jsxs("main",{className:"bg-primary/10 p-8 rounded-lg border border-primary/30 flex flex-col md:flex-row gap-5 shadow-md",children:[q.jsx("div",{className:"flex justify-center flex-1 md:flex-0",children:q.jsx(h1,{size:96,className:"bg-primary/20 text-primary p-3 rounded-full "})}),q.jsxs("aside",{className:"flex flex-col text-center md:text-left",children:[q.jsx("h3",{className:"text-2xl font-bold md:mb-1 mb-5",children:"Rate Limit Reached"}),q.jsx("p",{className:"mb-4 md:mb-2 text-md",children:"You've made to many requests in a short period. Please wait a moment."}),q.jsx("p",{className:"text-sm opacity-80 tracking-wide",children:"Try again in a few seconds for the best experience."})]})]})}),U0=()=>q.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-base-200 rounded-lg shadow-lg",children:[q.jsx("div",{className:"bg-primary/10 p-4 rounded-full mb-4",children:q.jsx(p1,{size:64,className:"text-primary"})}),q.jsx("h3",{className:"text-2xl font-bold tracking-wide text-primary",children:"No Notes Found"}),q.jsx("p",{className:"text-center text-base-content/50 md:max-w-md mt-8",children:'Start creating your first note by clicking the "Create Note" button in the navigation bar.'}),q.jsx(an,{to:"/create",className:"btn btn-primary mt-8 max-w-fit ml-auto flex items-center gap-1.5",children:"Create Your First Note"})]}),J2=()=>q.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-base-200 rounded-lg shadow-lg",children:[q.jsxs("h3",{className:"text-2xl font-bold tracking-wide text-error flex items-center gap-2.5",children:[q.jsx("span",{children:"Error Fetching Data"}),q.jsx(y1,{size:32,className:"text-error"})]}),q.jsxs("p",{className:"text-center text-base-content/50 md:max-w-md mt-8 flex items-center gap-1.5",children:[q.jsx(m1,{size:24,className:"text-info"}),q.jsx("span",{children:"There was an error fetching the data. Please try again later."})]})]}),F2=()=>{const{isRateLimited:n,isLoading:i,NotesData:r,setNotesData:o,isError:s}=H2();return q.jsxs("section",{className:"min-h-screen",children:[q.jsx(V2,{}),q.jsxs("article",{className:"py-5 my-5 container",children:[n&&q.jsx(k2,{}),r.length>0&&!n&&q.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5",children:r.map(f=>q.jsx(Z2,{note:f,setNotesData:o},f._id))}),r.length===0&&!i&&!n&&!s&&q.jsx(U0,{}),i&&q.jsx(K2,{}),s&&!i&&!n&&q.jsx(J2,{})]})]})},$2=({isLoading:n,NoteData:i,handlePut:r})=>{const{_id:o,title:s,content:f}=i||{},[d,y]=O.useState(""),[p,m]=O.useState("");O.useEffect(()=>{s&&f&&(y(s),m(f))},[s,f]);const g=async b=>{b.preventDefault();try{if(!d.trim()||!p.trim()){Ze.error("Please Fill All The Fields",{icon:"✍️"});return}if(d.length<3){Ze.error("Title Should Be At least 3 Characters Long");return}await r(o,{title:d,content:p})}catch(T){console.log(T)}};return i?q.jsxs("div",{className:"w-full max-w-3xl mx-auto bg-base-300 rounded-lg p-6 md:p-8 mt-10",children:[q.jsxs("h1",{className:"text-xl md:text-2xl font-bold mb-4 flex items-center gap-2.5",children:[q.jsx("span",{children:"Note Detail"}),q.jsx(Py,{size:24,className:"text-primary"})]}),q.jsxs("form",{onSubmit:g,children:[q.jsxs("div",{className:"form-control w-full",children:[q.jsxs("label",{className:"label mb-2.5",children:[q.jsx(Qn,{isLoading:n}),q.jsx("span",{className:"label-text",children:"Title"})]}),q.jsx("input",{type:"text",placeholder:"Note Title",value:d,onChange:b=>y(b.target.value),className:"input rounded-lg input-bordered outline-none border-none input-primary w-full"})]}),q.jsxs("div",{className:"form-control w-full mt-4 flex flex-col",children:[q.jsxs("label",{className:"label mb-2.5",children:[q.jsx(Qn,{isLoading:n}),q.jsx("span",{className:"label-text",children:"Content"})]}),q.jsx("textarea",{className:"textarea rounded-lg textarea-bordered h-48 w-full resize-none outline-none border-none textarea-primary",placeholder:"Write Your Note Here...",value:p,onChange:b=>m(b.target.value)})]}),q.jsx("div",{className:"card-actions justify-end",children:q.jsx("button",{type:"submit",disabled:n,className:"btn btn-primary mt-5 flex gap-1.5 items-center",children:n?q.jsxs(q.Fragment,{children:[q.jsx(Qn,{isLoading:n}),q.jsx("span",{children:"Saving..."})]}):q.jsxs(q.Fragment,{children:[q.jsx(v1,{size:16})," ",q.jsx("span",{children:"Save Changes"})]})})})]})]}):q.jsx(U0,{})},W2=()=>{const{id:n}=ub(),i=Zn(),{handlePut:r,isSaving:o}=Y2(),{handleDelete:s}=C0(),{NoteData:f,isLoading:d}=q2(n),y=async p=>{p.preventDefault(),i("/"),await s(n)};return q.jsxs("section",{className:"py-10 container",children:[q.jsxs("div",{className:"flex items-center justify-between gap-5",children:[q.jsxs(an,{to:"/",className:"btn btn-ghost btn-md",children:[q.jsx(Wy,{size:24}),q.jsx("span",{children:"Back To Notes"})]}),q.jsxs("button",{onClick:y,className:"btn btn-error btn-outline btn-md",children:[q.jsx(e0,{size:16}),q.jsx("span",{children:"Delete Note"})]})]}),q.jsx($2,{isLoading:d||o,NoteData:f,handlePut:r})]})},P2=Jb([{path:"/",element:q.jsx(F2,{})},{path:"/create",element:q.jsx(X2,{})},{path:"/note/:id",element:q.jsx(W2,{})}]);function I2(){return q.jsx(q.Fragment,{children:q.jsxs("div",{className:"relative min-h-screen w-full",children:[q.jsx("div",{className:"absolute insert-0 w-full h-full -z-10 items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,#000_60%,#00ff9d40_100%)]"}),q.jsx(uS,{}),q.jsx(xb,{router:P2})]})})}$v.createRoot(document.getElementById("root")).render(q.jsx(O.StrictMode,{children:q.jsx(I2,{})}));
