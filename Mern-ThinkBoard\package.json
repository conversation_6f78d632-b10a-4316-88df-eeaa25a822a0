{"name": "mern-thinkboard", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npm install --prefix Backend && npm install --prefix Frontend/thinkboard-app && npm run build --prefix Frontend/thinkboard-app", "start": "npm run start --prefix Backend"}, "repository": {"type": "git", "url": "git+https://github.com/mustafaalrawy-fedev/ThinkBoard-MernStack.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/mustafaalrawy-fedev/ThinkBoard-MernStack/issues"}, "homepage": "https://github.com/mustafaalrawy-fedev/ThinkBoard-MernStack#readme"}