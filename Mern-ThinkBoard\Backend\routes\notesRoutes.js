import express from 'express';
import { getAllNotes } from '../Controllers/notesController';

const router = express.Router();

router.get('/', getAllNotes);

router.post('/', (req, res) => {
  res.status(201).json({ message: 'Note created Successfully!' });
});

router.put('/:id', (req, res) => {
  res.status(200).json({ message: 'Note updated Successfully!' });
});

router.delete('/:id', (req, res) => {
  res.status(200).json({ message: 'Note deleted Successfully!' });
});

export default router;
